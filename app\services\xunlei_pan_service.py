import asyncio
import httpx
import logging
import time
import re
import random
import string
import json
from typing import Dict, Optional, Any, List
from contextlib import asynccontextmanager
from app.utils.config import settings
import threading
import datetime
from app.models.resource import PanResource
from tortoise.expressions import Q
from collections import Counter

# 配置日志
logger = logging.getLogger("xunlei-pan-service")
logger.setLevel(logging.INFO)

# 确保处理器只被添加一次
if not logger.handlers:
    # 移除所有现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 添加新的处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 防止日志向上传播
    logger.propagate = False

PAN_CONF = settings.get("pan_service", {})
DEFAULT_TIMEOUT = PAN_CONF.get("default_timeout", 30.0)
MAX_RETRIES = PAN_CONF.get("max_retries", 3)
RETRY_DELAY = PAN_CONF.get("retry_delay", 1.0)
MAX_CONCURRENT_TASKS = PAN_CONF.get("max_concurrent_tasks", 5)
DEFAULT_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}


@asynccontextmanager
async def get_client():
    """获取共享的HTTP客户端"""
    client = httpx.AsyncClient(
        timeout=DEFAULT_TIMEOUT,
        follow_redirects=True,
        http2=True,
        limits=httpx.Limits(
            max_connections=100,
            max_keepalive_connections=50,
        ),
        verify=False,
        transport=httpx.AsyncHTTPTransport(retries=MAX_RETRIES, verify=False),
    )
    try:
        yield client
    finally:
        await client.aclose()


class ConnectionPool:
    """HTTP连接池，提高性能并控制并发"""

    def __init__(self, max_size: int = 10):
        self.pool = []
        self.max_size = max_size
        self.semaphore = asyncio.Semaphore(max_size)

    async def get_client(self):
        """从连接池获取客户端"""
        async with self.semaphore:
            if self.pool:
                return self.pool.pop()
            return await self._create_client()

    async def _create_client(self):
        """创建HTTP客户端"""
        return httpx.AsyncClient(
            timeout=DEFAULT_TIMEOUT,
            follow_redirects=True,
            http2=True,
            limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
            verify=False,
        )

    async def release_client(self, client):
        """释放客户端到连接池"""
        if len(self.pool) < self.max_size:
            self.pool.append(client)
        else:
            await client.aclose()


connection_pool = ConnectionPool(max_size=MAX_CONCURRENT_TASKS)


class XunleiPanService:
    """
    迅雷网盘服务类，提供迅雷网盘的相关操作
    包括：用户信息获取、资源有效性校验、文件转存与分享等功能
    """

    def __init__(self):
        """初始化迅雷网盘服务"""
        logger.info("初始化迅雷网盘服务...")
        self.task_queue = asyncio.Queue()
        self.task_semaphore = asyncio.Semaphore(MAX_CONCURRENT_TASKS)
        self.xunlei_base_url = "https://api-pan.xunlei.com"
        self.xunlei_user_url = "https://xluser-ssl.xunlei.com"
        self._accounts_lock = threading.Lock()
        self._accounts = None
        self.session = None
        self.xunlei_headers = {
            **DEFAULT_HEADERS,
            "Host": "api-pan.xunlei.com",
            "Connection": "keep-alive",
            "Accept": "*/*",
        }
        # 添加token缓存及过期时间
        self._captcha_token = None
        self._captcha_expires_at = 0
        self._access_token = None
        self._access_expires_at = 0
        self._refresh_token = None
        logger.info("迅雷网盘服务初始化完成")

    async def initialize(self):
        """初始化服务会话"""
        logger.info("初始化迅雷网盘服务会话...")
        self.session = httpx.AsyncClient(
            timeout=DEFAULT_TIMEOUT,
            follow_redirects=True,
            http2=True,
            limits=httpx.Limits(
                max_connections=100,
                max_keepalive_connections=50,
            ),
            verify=False,
            transport=httpx.AsyncHTTPTransport(retries=MAX_RETRIES, verify=False),
        )
        # 预加载账户
        _ = self.accounts
        logger.info("迅雷网盘服务会话初始化完成")

    @property
    def accounts(self):
        """获取账户信息"""
        with self._accounts_lock:
            if self._accounts is None:
                self._accounts = self._load_config_accounts()
            return self._accounts

    def _load_config_accounts(self):
        """从配置加载账户信息"""
        try:
            accounts = {
                "xunlei_accounts": settings.get("xunlei_accounts", []),
            }
            return accounts
        except Exception as e:
            logger.error(f"加载账户信息失败: {str(e)}")
            return {"xunlei_accounts": []}

    def save_accounts(self):
        """保存账户信息"""
        logger.warning(
            "当前账户信息存储在config.yaml，不支持写回。请手动维护config.yaml。"
        )

    async def get_user_info(self, account_index: int = 0) -> Dict[str, Any]:
        """
        获取指定账户的用户信息
        :param account_index: 账户索引
        :return: 用户信息字典
        """
        if (
            not self.accounts.get("xunlei_accounts")
            or len(self.accounts["xunlei_accounts"]) <= account_index
        ):
            return {"status": "error", "message": "账户不存在"}
        return await self._get_xunlei_user_info(
            self.accounts["xunlei_accounts"][account_index], account_index
        )

    async def _get_xunlei_user_info(
        self, account: Dict[str, str], account_index: int
    ) -> Dict[str, Any]:
        """
        获取迅雷网盘用户信息
        :param account: 账户信息
        :param account_index: 账户索引
        :return: 用户信息字典
        """
        try:
            headers = {
                **DEFAULT_HEADERS,
                "authorization": account["authorization"],
                "x-client-id": account["x_client_id"],
                "x-device-id": account["x_device_id"],
                "x-device-sign": account["x_device_sign"],
                "Host": "xluser-ssl.xunlei.com",
                "Connection": "keep-alive",
                "Accept": "*/*",
            }
            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                # 获取用户基本信息
                url = self.xunlei_user_url + "/v1/user/me"
                response = await client.get(url, headers=headers)

                if response.status_code != 200:
                    logger.error(f"获取迅雷网盘用户信息失败: {response.status_code}")
                    return {"status": "error", "message": "获取用户信息失败"}

                data = response.json()
                print(f"获取迅雷网盘用户信息: {data}")
                # 更新账户信息
                self.accounts["xunlei_accounts"][account_index]["name"] = data.get(
                    "name", ""
                )
                self.accounts["xunlei_accounts"][account_index]["phone"] = data.get(
                    "phone_number", ""
                )

                return {
                    "status": "success",
                    "username": data.get("name", "未知用户"),
                    "phone": data.get("phone_number", ""),
                    "picture": data.get("picture", ""),
                    "sub": data.get("sub", ""),
                    "vip_info": data.get("vip_info", []),
                }
        except Exception as e:
            logger.error(f"获取迅雷网盘用户信息异常: {str(e)}")
            return {"status": "error", "message": f"发生异常: {str(e)}"}

    async def check_resource_status(self, share_url: str) -> dict:
        """
        检查迅雷网盘资源有效性
        :param share_url: 分享链接
        :return: 检查结果
        """
        if not share_url:
            return {"valid": False, "message": "分享链接为空"}
        try:
            if "pan.xunlei.com" not in share_url:
                return {"valid": False, "message": "非迅雷网盘链接"}
            return await self._check_xunlei_link(share_url)
        except Exception as e:
            logger.error(f"检查迅雷网盘资源状态异常: {str(e)}")
            return {"valid": False, "message": f"检查异常: {str(e)}"}

    async def _check_xunlei_link(self, share_url: str) -> dict:
        """
        检查迅雷网盘分享链接是否有效
        :param share_url: 分享链接
        :return: 检查结果
        """
        account = self.accounts["xunlei_accounts"][0]
        try:
            # 从分享链接中提取分享ID和提取码
            share_info = await self._extract_share_info(share_url)
            if not share_info:
                return {"valid": False, "message": "无效的迅雷网盘分享链接格式"}

            share_id = share_info.get("share_id")
            pwd = share_info.get("pwd") or ""

            # 获取验证令牌
            captcha_result = await self.get_captcha_token()
            if captcha_result.get("status") != "success":
                return {"valid": False, "message": "获取验证令牌失败"}

            captcha_token = captcha_result.get("captcha_token")

            # 调用API检查分享状态
            url = f"{self.xunlei_base_url}/drive/v1/share?share_id={share_id}&pass_code={pwd}&limit=100&pass_code_token=&page_token=&thumbnail_size=SIZE_SMALL"

            headers = {
                "x-captcha-token": captcha_token,
                "x-client-id": account["x_client_id"],
                "x-device-id": account["x_device_id"],
                "User-Agent": DEFAULT_HEADERS["User-Agent"],
                "Accept": "*/*",
                "Host": "api-pan.xunlei.com",
                "Connection": "keep-alive",
            }

            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                response = await client.get(url, headers=headers)
                if response.status_code != 200:
                    logger.error(f"检查迅雷网盘链接API调用失败: {response.status_code}")
                    return {"valid": False, "message": "检查链接失败"}

                data = response.json()

                # 根据share_status判断链接是否有效
                share_status = data.get("share_status", "")
                share_status_text = data.get("share_status_text", "")
                print(f"迅雷网盘链接检查结果: {share_status} - {share_status_text}")
                if share_status == "OK":
                    return {"valid": True, "message": "链接有效"}
                else:
                    logger.warning(
                        f"迅雷网盘链接无效: {share_status} - {share_status_text}"
                    )
                    return {
                        "valid": False,
                        "message": share_status_text or "分享链接已失效",
                    }
        except Exception as e:
            logger.error(f"检查迅雷网盘链接异常: {str(e)}")
            return {"valid": False, "message": f"链接异常: {str(e)}"}

    async def _extract_share_info(self, share_url: str) -> Optional[Dict[str, str]]:
        """
        从迅雷网盘分享链接中提取分享ID和提取码

        Args:
            share_url: 分享链接

        Returns:
            Dict包含share_id和pwd，如果提取失败返回None
        """
        try:
            # 提取分享ID
            share_id_match = re.search(
                r"pan\.xunlei\.com/s/([a-zA-Z0-9_-]+)", share_url
            )
            if not share_id_match:
                logger.error("无效的迅雷网盘分享链接格式")
                return None

            share_id = share_id_match.group(1)
            logger.info(f"提取到迅雷网盘分享ID: {share_id}")

            # 尝试从链接中提取提取码
            pwd = None
            pwd_match = re.search(r"pwd=([a-zA-Z0-9]+)", share_url)
            if pwd_match:
                pwd = pwd_match.group(1)
                logger.info(f"从链接中提取到提取码: {pwd}")

            return {"share_id": share_id, "pwd": pwd}
        except Exception as e:
            logger.error(f"提取迅雷网盘分享信息异常: {str(e)}")
            return None

    async def get_captcha_token(self) -> Dict[str, Any]:
        """
        获取验证令牌，有缓存策略
        :return: 包含captcha_token的字典
        """
        # 检查当前token是否有效
        current_time = int(time.time())
        if self._captcha_token and self._captcha_expires_at > current_time:
            logger.debug("使用缓存的captcha_token")
            return {
                "status": "success",
                "captcha_token": self._captcha_token,
                "expires_in": self._captcha_expires_at - current_time,
            }

        logger.info("更新captcha_token...")
        try:
            url = f"{self.xunlei_user_url}/v1/shield/captcha/init"
            # 获取账户信息
            if (
                not self.accounts.get("xunlei_accounts")
                or len(self.accounts["xunlei_accounts"]) == 0
            ):
                return {"status": "error", "message": "未配置账户信息"}

            account = self.accounts["xunlei_accounts"][0]
            client_id = account.get("x_client_id", "Xqp0kJBXWhwaTpB6")
            device_id = account.get("x_device_id", "8288479c4fb9ebed09ad6bb8e2605cae")

            payload = json.dumps(
                {
                    "client_id": client_id,
                    "action": "get:/drive/v1/tasks",
                    "device_id": device_id,
                    "meta": {
                        "username": "",
                        "phone_number": "",
                        "email": "",
                        "package_name": "pan.xunlei.com",
                        "client_version": account.get("client_version", "1.91.23"),
                        "captcha_sign": account.get(
                            "captcha_sign", "1.f2b77037ff11dc32ef3c9fa3d9e1142c"
                        ),
                        "timestamp": account.get("timestamp", "*************"),
                        "user_id": account.get("user_id", "*********"),
                    },
                }
            )

            headers = {
                "User-Agent": DEFAULT_HEADERS["User-Agent"],
                "Content-Type": "application/json",
                "Accept": "*/*",
                "Origin": "https://pan.xunlei.com",
                "Referer": "https://pan.xunlei.com/",
                "Host": "xluser-ssl.xunlei.com",
                "Connection": "keep-alive",
            }
            logger.info(f"获取验证令牌请求体: {payload}")
            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                response = await client.post(url, headers=headers, data=payload)
                if response.status_code != 200:
                    logger.error(f"获取验证令牌失败: {response.status_code}")
                    return {"status": "error", "message": "获取验证令牌失败"}

                data = response.json()
                captcha_token = data.get("captcha_token")
                if not captcha_token:
                    return {"status": "error", "message": "未获取到验证令牌"}

                # 更新token和过期时间
                expires_in = data.get("expires_in", 300)
                self._captcha_token = captcha_token
                self._captcha_expires_at = int(time.time()) + expires_in

                logger.info(f"新的captcha_token已获取，有效期{expires_in}秒")

                return {
                    "status": "success",
                    "captcha_token": captcha_token,
                    "expires_in": expires_in,
                }
        except Exception as e:
            logger.error(f"获取验证令牌异常: {str(e)}")
            return {"status": "error", "message": f"获取验证令牌异常: {str(e)}"}

    async def get_authorization_token(self, account_index: int = 0) -> Dict[str, Any]:
        """
        获取或刷新授权令牌
        :param account_index: 账户索引
        :return: 包含access_token的字典
        """
        # 检查当前token是否有效
        current_time = int(time.time())
        if self._access_token and self._access_expires_at > current_time:
            logger.debug("使用缓存的access_token")
            return {
                "status": "success",
                "access_token": self._access_token,
                "expires_in": self._access_expires_at - current_time,
            }

        logger.info("更新access_token...")
        try:
            # 获取账户信息
            if (
                not self.accounts.get("xunlei_accounts")
                or len(self.accounts["xunlei_accounts"]) <= account_index
            ):
                return {"status": "error", "message": "账户不存在"}

            account = self.accounts["xunlei_accounts"][account_index]
            refresh_token = account.get("refresh_token")

            if not refresh_token:
                return {"status": "error", "message": "账户未配置refresh_token"}

            url = f"{self.xunlei_user_url}/v1/auth/token"

            payload = json.dumps(
                {
                    "client_id": account.get("x_client_id", "Xqp0kJBXWhwaTpB6"),
                    "grant_type": "refresh_token",
                    "refresh_token": refresh_token,
                }
            )

            headers = {
                "Content-Type": "application/json",
                "Origin": "https://pan.xunlei.com",
                "Referer": "https://pan.xunlei.com/",
                "User-Agent": DEFAULT_HEADERS["User-Agent"],
                "x-action": "401",
                "x-client-id": account.get("x_client_id", "Xqp0kJBXWhwaTpB6"),
                "x-device-id": account.get(
                    "x_device_id", "8288479c4fb9ebed09ad6bb8e2605cae"
                ),
                "x-device-sign": account.get("x_device_sign", ""),
                "x-protocol-version": "301",
                "x-sdk-version": "3.4.20",
            }

            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                response = await client.post(url, headers=headers, data=payload)
                if response.status_code != 200:
                    logger.error(f"获取授权令牌失败: {response.status_code}")
                    return {"status": "error", "message": "获取授权令牌失败"}

                data = response.json()
                access_token = data.get("access_token")
                refresh_token = data.get("refresh_token")

                if not access_token or not refresh_token:
                    return {"status": "error", "message": "未获取到授权令牌"}

                # 更新token和过期时间 (默认12小时有效期)
                self._access_token = access_token
                self._refresh_token = refresh_token
                self._access_expires_at = int(time.time()) + 43200  # 12小时

                # 更新账户信息
                self.accounts["xunlei_accounts"][account_index][
                    "authorization"
                ] = f"Bearer {access_token}"
                self.accounts["xunlei_accounts"][account_index][
                    "refresh_token"
                ] = refresh_token

                logger.info("新的access_token和refresh_token已获取")

                return {
                    "status": "success",
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "expires_in": 43200,
                }
        except Exception as e:
            logger.error(f"获取授权令牌异常: {str(e)}")
            return {"status": "error", "message": f"获取授权令牌异常: {str(e)}"}

    async def get_share_info(
        self, share_id: str, pass_code: str, captcha_token: str
    ) -> Dict[str, Any]:
        """
        获取分享文件信息
        :param share_id: 分享ID
        :param pass_code: 提取码
        :param captcha_token: 验证令牌
        :return: 分享文件信息
        """
        account = self.accounts["xunlei_accounts"][0]
        try:
            url = f"{self.xunlei_base_url}/drive/v1/share?share_id={share_id}&pass_code={pass_code}&limit=100&pass_code_token=&page_token=&thumbnail_size=SIZE_SMALL"

            headers = {
                "x-captcha-token": captcha_token,
                "x-client-id": account.get("x_client_id", "Xqp0kJBXWhwaTpB6"),
                "x-device-id": account.get(
                    "x_device_id", "8288479c4fb9ebed09ad6bb8e2605cae"
                ),
                "User-Agent": DEFAULT_HEADERS["User-Agent"],
                "Accept": "*/*",
                "Host": "api-pan.xunlei.com",
                "Connection": "keep-alive",
            }

            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                response = await client.get(url, headers=headers)
                if response.status_code != 200:
                    logger.error(f"获取分享文件信息失败: {response.json()}")
                    return {"status": "error", "message": "获取分享文件信息失败"}

                data = response.json()

                # 检查分享状态
                if data.get("share_status") != "OK":
                    return {
                        "status": "error",
                        "message": data.get("share_status_text", "分享状态异常"),
                    }

                # 提取需要的信息
                pass_code_token = data.get("pass_code_token", "")
                title = data.get("title", "")
                files = data.get("files", [])
                file_ids = [file.get("id") for file in files if file.get("id")]

                if not pass_code_token or not title or not file_ids:
                    return {"status": "error", "message": "获取分享信息关键字段失败"}

                # 改造这一块，支持获取文件列表
                return {
                    "status": "success",
                    "pass_code_token": pass_code_token,
                    "title": title,
                    "file_ids": file_ids,
                    "file_num": data.get("file_num", "0"),
                    "files": files,
                }
        except Exception as e:
            logger.error(f"获取分享文件信息异常: {str(e)}")
            return {"status": "error", "message": f"获取分享文件信息异常: {str(e)}"}

    async def create_folder(
        self, folder_name: str, account: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        创建新文件夹
        :param folder_name: 文件夹名称
        :param account: 账户信息
        :return: 创建结果
        """
        try:
            # 先获取新的验证令牌
            captcha_result = await self.get_captcha_token()
            if captcha_result.get("status") != "success":
                return captcha_result

            captcha_token = captcha_result.get("captcha_token")

            url = f"{self.xunlei_base_url}/drive/v1/files"

            payload = json.dumps(
                {
                    "parent_id": "",
                    "name": folder_name,
                    "kind": "drive#folder",
                    "space": "",
                }
            )

            headers = {
                "authorization": account["authorization"],
                "x-captcha-token": captcha_token,
                "x-client-id": account["x_client_id"],
                "x-device-id": account["x_device_id"],
                "User-Agent": DEFAULT_HEADERS["User-Agent"],
                "Content-Type": "application/json",
                "Accept": "*/*",
                "Host": "api-pan.xunlei.com",
                "Connection": "keep-alive",
            }

            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                response = await client.post(url, headers=headers, data=payload)
                if response.status_code != 200:
                    logger.info(f"创建文件夹响应: {response.json()}")
                    logger.error(f"创建文件夹失败: {response.status_code}")
                    return {"status": "error", "message": "创建文件夹失败"}

                data = response.json()
                file = data.get("file", {})
                folder_id = file.get("id")

                if not folder_id:
                    return {
                        "status": "error",
                        "message": "创建文件夹失败，未获取到文件夹ID",
                    }

                return {
                    "status": "success",
                    "folder_id": folder_id,
                    "folder_name": file.get("name"),
                    "created_time": file.get("created_time"),
                }
        except Exception as e:
            logger.error(f"创建文件夹异常: {str(e)}")
            return {"status": "error", "message": f"创建文件夹异常: {str(e)}"}

    async def create_restore_task(
        self,
        share_id: str,
        pass_code_token: str,
        file_ids: List[str],
        folder_id: str,
        account: Dict[str, str],
    ) -> Dict[str, Any]:
        """
        创建转存任务
        :param share_id: 分享ID
        :param pass_code_token: 提取码令牌
        :param file_ids: 文件ID列表
        :param folder_id: 文件夹ID
        :param account: 账户信息
        :return: 创建结果
        """
        try:
            # 先获取新的验证令牌
            captcha_result = await self.get_captcha_token()
            if captcha_result.get("status") != "success":
                return captcha_result

            captcha_token = captcha_result.get("captcha_token")

            url = f"{self.xunlei_base_url}/drive/v1/share/restore"

            payload = json.dumps(
                {
                    "parent_id": "",
                    "share_id": share_id,
                    "pass_code_token": pass_code_token,
                    "ancestor_ids": [],
                    "file_ids": file_ids,
                    "folder_id": folder_id,
                    "specify_parent_id": True,
                }
            )

            headers = {
                "authorization": account["authorization"],
                "x-captcha-token": captcha_token,
                "x-client-id": account["x_client_id"],
                "x-device-id": account["x_device_id"],
                "User-Agent": DEFAULT_HEADERS["User-Agent"],
                "Content-Type": "application/json",
                "Accept": "*/*",
                "Host": "api-pan.xunlei.com",
                "Connection": "keep-alive",
            }

            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                response = await client.post(url, headers=headers, data=payload)
                if response.status_code != 200:
                    logger.error(f"创建转存任务失败: {response.status_code}")
                    return {"status": "error", "message": "创建转存任务失败"}

                data = response.json()
                restore_task_id = data.get("restore_task_id")
                share_status = data.get("share_status")
                logger.info(f"创建转存任务响应: {data}")
                if not restore_task_id or share_status != "OK":
                    return {
                        "status": "error",
                        "message": "创建转存任务失败，未获取到任务ID",
                    }

                return {
                    "status": "success",
                    "restore_task_id": restore_task_id,
                    "restore_status": share_status,
                }
        except Exception as e:
            logger.error(f"创建转存任务异常: {str(e)}")
            return {"status": "error", "message": f"创建转存任务异常: {str(e)}"}

    async def query_task_status(
        self, task_id: str, account: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        查询任务状态
        :param task_id: 任务ID
        :param account: 账户信息
        :return: 任务状态
        """
        try:
            # 先获取新的验证令牌
            captcha_result = await self.get_captcha_token()
            if captcha_result.get("status") != "success":
                return captcha_result

            captcha_token = captcha_result.get("captcha_token")

            url = f"{self.xunlei_base_url}/drive/v1/tasks/{task_id}"

            headers = {
                "authorization": account["authorization"],
                "x-captcha-token": captcha_token,
                "x-client-id": account["x_client_id"],
                "x-device-id": account["x_device_id"],
                "User-Agent": DEFAULT_HEADERS["User-Agent"],
                "Accept": "*/*",
                "Host": "api-pan.xunlei.com",
                "Connection": "keep-alive",
            }

            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                response = await client.get(url, headers=headers)
                if response.status_code != 200:
                    logger.error(f"查询任务状态失败: {response.status_code}")
                    return {"status": "error", "message": "查询任务状态失败"}

                data = response.json()
                file_id = data.get("file_id", "")
                phase = data.get("phase", "")
                progress = data.get("progress", 0)

                if phase != "PHASE_TYPE_COMPLETE" and progress < 100:
                    return {
                        "status": "pending",
                        "message": "任务处理中",
                        "progress": progress,
                        "phase": phase,
                    }

                return {
                    "status": "success",
                    "file_id": file_id,
                    "phase": phase,
                    "progress": progress,
                    "message": data.get("message", ""),
                }
        except Exception as e:
            logger.error(f"查询任务状态异常: {str(e)}")
            return {"status": "error", "message": f"查询任务状态异常: {str(e)}"}

    async def share_file(self, file_id: str, account: Dict[str, str]) -> Dict[str, Any]:
        """
        分享文件
        :param file_id: 文件ID
        :param account: 账户信息
        :return: 分享结果
        """
        try:
            # 先获取新的验证令牌
            captcha_result = await self.get_captcha_token()
            if captcha_result.get("status") != "success":
                return captcha_result

            captcha_token = captcha_result.get("captcha_token")

            url = f"{self.xunlei_base_url}/drive/v1/share"

            payload = json.dumps(
                {
                    "file_ids": [file_id],
                    "share_to": "copy",
                    "params": {"subscribe_push": "false", "WithPassCodeInLink": "true"},
                    "title": "云盘资源分享",
                    "restore_limit": "-1",
                    "expiration_days": "1",
                }
            )

            headers = {
                "authorization": account["authorization"],
                "x-captcha-token": captcha_token,
                "x-client-id": account["x_client_id"],
                "x-device-id": account["x_device_id"],
                "User-Agent": DEFAULT_HEADERS["User-Agent"],
                "Content-Type": "application/json",
                "Accept": "*/*",
                "Host": "api-pan.xunlei.com",
                "Connection": "keep-alive",
            }

            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                response = await client.post(url, headers=headers, data=payload)
                if response.status_code != 200:
                    logger.error(f"分享文件失败: {response.status_code}")
                    return {"status": "error", "message": "分享文件失败"}

                data = response.json()
                share_url = data.get("share_url", "")
                pass_code = data.get("pass_code", "")
                share_text = data.get("share_text", "")

                # 提取分享链接
                share_link_match = re.search(
                    r"(https://pan\.xunlei\.com/s/[a-zA-Z0-9_-]+\?pwd=[a-zA-Z0-9]+#)",
                    share_text,
                )
                if share_link_match:
                    share_link = share_link_match.group(1)
                else:
                    share_link = f"{share_url}?pwd={pass_code}#"

                return {
                    "status": "success",
                    "share_link": share_link,
                    "share_id": data.get("share_id", ""),
                    "pass_code": pass_code,
                }
        except Exception as e:
            logger.error(f"分享文件异常: {str(e)}")
            return {"status": "error", "message": f"分享文件异常: {str(e)}"}

    async def save_and_share(
        self, share_url: str, account_index: int = 0, share_pwd: str = None
    ) -> Dict[str, Any]:
        """
        转存并分享文件的主流程
        :param share_url: 分享链接
        :param account_index: 账户索引
        :return: 处理结果
        """
        logger.info(f"开始处理迅雷网盘分享文件: {share_url}")
        total_start = time.time()
        try:
            # 获取账户信息
            if (
                not self.accounts.get("xunlei_accounts")
                or len(self.accounts["xunlei_accounts"]) <= account_index
            ):
                return {"status": "error", "message": "账户不存在"}

            account = self.accounts["xunlei_accounts"][account_index]

            # 1. 提取分享链接信息
            step_start = time.time()
            share_info = await self._extract_share_info(share_url)
            if not share_info:
                return {"status": "error", "message": "无效的分享链接格式"}
            logger.info(f"步骤1-提取分享信息 耗时: {time.time() - step_start:.2f}秒")

            # 2. 获取验证令牌
            step_start = time.time()
            captcha_result = await self.get_captcha_token()
            if captcha_result.get("status") != "success":
                return captcha_result
            captcha_token = captcha_result.get("captcha_token")
            logger.info(f"步骤2-获取验证令牌 耗时: {time.time() - step_start:.2f}秒")

            # 3. 获取分享文件信息
            step_start = time.time()
            share_detail = await self.get_share_info(
                share_info["share_id"], share_info["pwd"] or "", captcha_token
            )
            if share_detail.get("status") != "success":
                return share_detail
            title = share_detail.get("title")
            file_ids = share_detail.get("file_ids")
            pass_code_token = share_detail.get("pass_code_token")
            logger.info(
                f"步骤3-获取分享文件信息 耗时: {time.time() - step_start:.2f}秒"
            )

            # 4. 创建新文件夹
            step_start = time.time()
            # 生成随机3个字母
            random_suffix = "".join(random.choices(string.ascii_letters, k=3))
            folder_name = f"{title}_{random_suffix}"
            folder_result = await self.create_folder(folder_name, account)
            if folder_result.get("status") != "success":
                return folder_result
            folder_id = folder_result.get("folder_id")
            logger.info(f"步骤4-创建新文件夹 耗时: {time.time() - step_start:.2f}秒")

            # 5. 创建转存任务
            step_start = time.time()
            restore_result = await self.create_restore_task(
                share_info["share_id"], pass_code_token, file_ids, folder_id, account
            )
            if restore_result.get("status") != "success":
                return restore_result
            restore_task_id = restore_result.get("restore_task_id")
            # 转存任务完成后，文件ID为文件夹ID
            restore_file_id = folder_id
            logger.info(f"步骤5-创建转存任务 耗时: {time.time() - step_start:.2f}秒")

            # 6. 查询任务状态，等待任务完成
            step_start = time.time()
            max_attempts = 10
            for attempt in range(max_attempts):
                task_status = await self.query_task_status(restore_task_id, account)
                if task_status.get("message") == "完成":
                    break
                elif task_status.get("status") == "error":
                    return task_status

                logger.info(
                    f"任务处理中，进度: {task_status.get('progress', 0)}%，等待..."
                )
                await asyncio.sleep(1)  # 等待1秒后再次查询

                if attempt == max_attempts - 1:
                    return {"status": "error", "message": "任务处理超时"}

            file_id = restore_file_id
            if not file_id:
                return {"status": "error", "message": "未获取到文件ID"}
            logger.info(f"步骤6-查询任务状态 耗时: {time.time() - step_start:.2f}秒")

            # 7. 分享文件
            step_start = time.time()
            share_result = await self.share_file(file_id, account)
            if share_result.get("status") != "success":
                return share_result
            logger.info(f"步骤7-分享文件 耗时: {time.time() - step_start:.2f}秒")

            # 总耗时统计
            total_time = time.time() - total_start
            logger.info(f"迅雷网盘文件保存与分享总耗时: {total_time:.2f}秒")
            logger.info(f"迅雷网盘分享结果: {share_result}")
            return {
                "status": "success",
                "message": "文件已成功转存并分享",
                "share_url": share_result.get("share_link"),
                "share_pwd": share_result.get("pass_code", ""),
                "time_usage": {"total": total_time},
            }
        except Exception as e:
            logger.error(f"保存迅雷网盘分享文件异常: {str(e)}")
            return {"status": "error", "message": f"发生异常: {str(e)}"}

    async def _get_folder_contents_recursive(
        self,
        share_id: str,
        pass_code: str,
        pass_code_token: str,
        folder_id: str,
        account: Dict[str, str],
        captcha_token: str,
    ) -> List[Dict[str, Any]]:
        """
        递归获取迅雷分享中的文件夹内容。
        """
        all_files = []
        page_token = ""

        while True:
            try:
                # 访问下一级文件夹时，使用 /drive/v1/share/detail 接口
                url = f"{self.xunlei_base_url}/drive/v1/share/detail"
                params = {
                    "share_id": share_id,
                    "parent_id": folder_id,
                    "limit": 100,
                    "pass_code_token": pass_code_token,
                    "page_token": page_token,
                    "thumbnail_size": "SIZE_SMALL",
                }

                headers = {
                    "x-captcha-token": captcha_token,
                    "x-client-id": account["x_client_id"],
                    "x-device-id": account["x_device_id"],
                    "User-Agent": DEFAULT_HEADERS["User-Agent"],
                    "Accept": "*/*",
                    "Host": "api-pan.xunlei.com",
                    "Connection": "keep-alive",
                }

                async with httpx.AsyncClient(timeout=20.0, verify=False) as client:
                    # 使用 client.build_request 构造并记录最终的请求 URL，以便调试
                    final_url = client.build_request("GET", url, params=params).url
                    logger.info(f"请求文件夹内容: {final_url}")
                    logger.info(f"一次调用, 文件夹ID: {folder_id}")
                    response = await client.get(url, params=params, headers=headers)

                if response.status_code != 200:
                    logger.error(
                        f"获取文件夹 '{folder_id}' 内容失败: {response.status_code} {response.text}"
                    )
                    break

                data = response.json()
                if data.get("share_status") != "OK":
                    logger.error(
                        f"获取文件夹 '{folder_id}' 内容失败: {data.get('share_status_text')}"
                    )
                    break

                for item in data.get("files", []):
                    if item.get("kind") == "drive#folder":
                        logger.info(f"二次调用, 文件夹ID: {item['id']}")
                        sub_folder_files = await self._get_folder_contents_recursive(
                            share_id,
                            pass_code,
                            pass_code_token,
                            item["id"],
                            account,
                            captcha_token,
                        )
                        all_files.extend(sub_folder_files)
                    else:
                        all_files.append(item)

                page_token = data.get("next_page_token", "")
                if not page_token:
                    break
            except Exception as e:
                logger.error(f"获取文件夹 '{folder_id}' 内容时发生异常: {e}")
                import traceback

                logger.error(traceback.format_exc())
                break

        return all_files

    async def _get_resource_details(self, resource: PanResource) -> dict:
        """
        从迅雷网盘获取资源详情，智能处理各种分享场景。
        """
        logger.info(f"开始获取迅雷网盘资源详情: {resource.original_url}")
        account = self.accounts["xunlei_accounts"][0]

        try:
            share_info = await self._extract_share_info(resource.original_url)
            if not share_info:
                return {"status": "error", "message": "无效的迅雷分享链接格式"}

            share_id = share_info["share_id"]
            pwd = share_info.get("pwd") or ""

            captcha_result = await self.get_captcha_token()
            if captcha_result.get("status") != "success":
                return captcha_result
            captcha_token = captcha_result["captcha_token"]

            url = f"{self.xunlei_base_url}/drive/v1/share?share_id={share_id}&pass_code={pwd}&limit=100&pass_code_token=&page_token=&thumbnail_size=SIZE_SMALL"
            headers = {
                "x-captcha-token": captcha_token,
                "x-client-id": account["x_client_id"],
                "x-device-id": account["x_device_id"],
                "User-Agent": DEFAULT_HEADERS["User-Agent"],
            }

            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                response = await client.get(url, headers=headers)

            if response.status_code != 200:
                return {
                    "status": "error",
                    "message": f"API请求失败: {response.status_code}",
                }

            data = response.json()
            if data.get("share_status") != "OK":
                return {
                    "status": "error",
                    "message": data.get("share_status_text", "分享链接无效"),
                }

            pass_code_token = data.get("pass_code_token", "")
            user_info = data.get("user_info", {})
            author_name = user_info.get("nickname", "")
            author_avatar = user_info.get("avatar", "")
            title = data.get("title", "")

            all_files = []
            root_files = data.get("files", [])
            for item in root_files:
                if item.get("kind") == "drive#folder":
                    folder_files = await self._get_folder_contents_recursive(
                        share_id,
                        pwd,
                        pass_code_token,
                        item["id"],
                        account,
                        captcha_token,
                    )
                    all_files.extend(folder_files)
                else:
                    all_files.append(item)

            total_size = sum(int(file.get("size", "0")) for file in all_files)

            file_type_map = {
                "VIDEO": "视频",
                "AUDIO": "音频",
                "IMAGE": "图片",
                "DOC": "文档",
                "APP": "应用",
                "ARCHIVE": "压缩包",
                "OTHER": "其他",
            }
            file_type = "其他"
            if all_files:
                categories = [file.get("file_category", "OTHER") for file in all_files]
                most_common_category = Counter(categories).most_common(1)[0][0]
                file_type = file_type_map.get(most_common_category, "其他")

            return {
                "status": "success",
                "all_files": all_files,
                "share_id": share_id,
                "title": title or resource.title,
                "author_name": author_name,
                "author_avatar": author_avatar,
                "file_type": file_type,
                "share_url": resource.original_url,
                "file_size": total_size,
                "expiration_at": data.get("expiration_at"),
            }

        except Exception as e:
            logger.error(f"获取迅雷资源详情异常: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            return {"status": "error", "message": f"获取资源详情异常: {str(e)}"}

    async def fetch_and_update_resource_details(
        self, resource: PanResource, is_parsed: bool = True
    ) -> bool:
        """
        获取并更新迅雷网盘资源详情。
        """
        logger.info(f"开始处理迅雷资源: {resource.resource_key}")
        start_time = time.time()

        resource_details = await self._get_resource_details(resource)
        if resource_details.get("status") != "success":
            logger.error(f"获取资源详情失败: {resource_details.get('message')}")
            resource.verified_status = "invalid"
            resource.remark = resource_details.get("message")
            await resource.save()
            return False

        try:
            # 注意：不再强制设置is_parsed=True，保持用户提交时设置的值
            resource.verified_status = "valid"

            # 根据is_parsed参数决定是否填充字段
            if is_parsed:
                # is_parsed=True: 填充完整字段
                resource.share_url = resource.original_url
                # 检查是否为用户自定义标题
                if resource.title and not resource.title.startswith("待解析资源:"):
                    logger.info(f"保持用户自定义标题不变: {resource.title}")
                else:
                    resource.title = resource_details.get("title", resource.title)
                resource.author = resource_details.get("author_name", resource.author)
                resource.author_avatar = resource_details.get("author_avatar", "")

                all_files = resource_details.get("all_files", [])
                # 直接从all_files构建text_content，并限制最多20个文件
                resource.text_content = "\n".join(
                    [
                        f"{f.get('name', '未知文件名')} ({f.get('size', '未知大小')})"
                        for f in all_files[:20]
                    ]
                )

                resource.file_type = resource_details.get("file_type", "其他")

                logger.info(f"is_parsed=True: 填充完整字段，author={resource.author}")
            else:
                # is_parsed=False: 仅填充基本字段，author设为97_bot
                resource.author = "97_bot"
                # 检查是否为用户自定义标题
                if resource.title and not resource.title.startswith("待解析资源:"):
                    logger.info(
                        f"is_parsed=False: 保持用户自定义标题不变: {resource.title}"
                    )
                else:
                    resource.title = resource_details.get("title", resource.title)

                all_files = resource_details.get("all_files", [])
                # 直接从all_files构建text_content，并限制最多20个文件
                resource.text_content = "\n".join(
                    [
                        f"{f.get('name', '未知文件名')} ({f.get('size', '未知大小')})"
                        for f in all_files[:20]
                    ]
                )

                resource.file_type = resource_details.get("file_type", "其他")
                # 不填充share_url和author_avatar
                logger.info(f"is_parsed=False: 仅填充基本字段，author=97_bot")

            total_size = resource_details.get("file_size", 0)
            if total_size < 1024:
                resource.file_size = f"{total_size}B"
            elif total_size < 1024**2:
                resource.file_size = f"{total_size/1024:.2f}KB"
            elif total_size < 1024**3:
                resource.file_size = f"{total_size/1024**2:.2f}MB"
            else:
                resource.file_size = f"{total_size/1024**3:.2f}GB"

            expiry_str = resource_details.get("expiration_at")
            if expiry_str:
                # 迅雷的-1代表永久，但API返回的是一个具体时间，需要根据expiration_left判断
                if resource_details.get("expiration_left", "0") == "-1":
                    resource.expiry_date = None  # 永久
                else:
                    try:
                        resource.expiry_date = datetime.datetime.fromisoformat(
                            expiry_str.replace("+08:00", "+08:00")
                        )
                    except:
                        resource.expiry_date = None

            if all_files:
                latest_mtime_str = all_files[0].get("modified_time")
                if latest_mtime_str:
                    resource.updated_at = datetime.datetime.fromisoformat(
                        latest_mtime_str.replace("+08:00", "+08:00")
                    )

            await resource.save()
            logger.info(
                f"资源 {resource.resource_key} 已成功更新，耗时: {time.time() - start_time:.2f}秒"
            )
            return True

        except Exception as e:
            logger.error(f"更新资源对象失败: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return False


# 创建单例实例
def get_xunlei_pan_service():
    return XunleiPanService()


xunlei_pan_service = get_xunlei_pan_service()


async def test_get_xunlei_user_info():
    """测试获取迅雷网盘用户信息"""
    print("开始测试迅雷网盘用户信息获取...")

    # 获取迅雷网盘服务实例
    xunlei_service = get_xunlei_pan_service()

    # 初始化服务会话
    await xunlei_service.initialize()
    # get_token_result = await xunlei_service.get_authorization_token(account_index=0)
    share_result = await xunlei_service.check_resource_status(
        share_url="https://pan.xunlei.com/s/VON3KRv736kS38iYnUXsRxukA1?pwd=2pab"
    )
    print(f"分享结果: {share_result}")
    # print(f"获取token结果: {get_token_result}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_get_xunlei_user_info())

目标:
    1、用户提交的资源未解析的不同步，成功解析成功的同步到sync表
    2、用户提交的资源必须全部填充用户名称和用户头像
    3、爬虫数据自动同步到sync，不做限制


现状：
    1、用户提交的资源没有填充用户名和头像，原因is_parsed（有歧义，这个应该是是否是需要解析的资源），4个网盘的处理统一设置成了false，导致解析的时候
    固定填充了97_bot，而share_url、author_avatar、expiry_date、均不填充
    2、触发器针对以上不填充的内容不再同步到sync表


当前已验证数据同步没问题的爬虫有[所有爬虫均无问题]：
1、aisoua爬虫
2、kdoc爬虫
3、duanju爬虫
4、telegram爬虫

修改方案:
1、针对is_parsed填充的数据，用户提交的仍然设置为False，但是需要解析完成后将标志设置为True，并且填充share_url、author_avatar、expiry_date，author为
解析出来的author内容
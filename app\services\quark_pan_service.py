import asyncio
import httpx
import logging
import time
import re
import random
from typing import Dict, Optional, Any, List
from contextlib import asynccontextmanager
from app.models.resource import PanResource
from app.utils.config import settings
import threading
import json
from datetime import datetime, timedelta, timezone

logger = logging.getLogger("quark-pan-service")

# 定义北京时间时区（UTC+8）
BEIJING_TIMEZONE = timezone(timedelta(hours=8))

PAN_CONF = settings.get("pan_service", {})
DEFAULT_TIMEOUT = PAN_CONF.get("default_timeout", 30.0)
MAX_RETRIES = PAN_CONF.get("max_retries", 3)
RETRY_DELAY = PAN_CONF.get("retry_delay", 0.8)
MAX_CONCURRENT_TASKS = PAN_CONF.get("max_concurrent_tasks", 5)
DEFAULT_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}


@asynccontextmanager
async def get_client():
    """获取共享HTTP客户端"""
    client = httpx.AsyncClient(
        timeout=DEFAULT_TIMEOUT,
        follow_redirects=True,
        http2=True,
        limits=httpx.Limits(
            max_connections=100,
            max_keepalive_connections=50,
        ),
        verify=False,
        transport=httpx.AsyncHTTPTransport(retries=MAX_RETRIES, verify=False),
    )
    try:
        yield client
    finally:
        await client.aclose()


class ConnectionPool:
    """HTTP连接池，提高性能并控制并发"""

    def __init__(self, max_size: int = 10):
        self.pool = []
        self.max_size = max_size
        self.semaphore = asyncio.Semaphore(max_size)

    async def get_client(self):
        """从连接池获取客户端"""
        async with self.semaphore:
            if self.pool:
                return self.pool.pop()
            return await self._create_client()

    async def _create_client(self):
        """创建HTTP客户端"""
        return httpx.AsyncClient(
            timeout=DEFAULT_TIMEOUT,
            follow_redirects=True,
            http2=True,
            limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
            verify=False,
        )

    async def release_client(self, client):
        """释放客户端到连接池"""
        if len(self.pool) < self.max_size:
            self.pool.append(client)
        else:
            await client.aclose()


connection_pool = ConnectionPool(max_size=MAX_CONCURRENT_TASKS)


class QuarkPanService:
    """
    夸克网盘服务类，提供夸克网盘的相关操作
    包括：用户信息获取、文件转存、分享创建等功能
    """

    def __init__(self, use_proxy: bool = False):
        """初始化夸克网盘服务"""
        logger.info("初始化夸克网盘服务...")
        self.use_proxy = use_proxy
        self.task_queue = asyncio.Queue()
        self.task_semaphore = asyncio.Semaphore(MAX_CONCURRENT_TASKS)
        # 代理和直连的URL
        self.quark_base_url_proxy = "https://14.103.234.187/quark/"
        self.quark_drive_pc_base_url_proxy = "https://14.103.234.187/quark-drive-pc/"
        self.quark_drive_h_base_url_proxy = "https://14.103.234.187/quark-drive-h/"
        self.quark_base_url_direct = "https://pan.quark.cn/"
        self.quark_drive_pc_base_url_direct = "https://drive-pc.quark.cn/"
        self.quark_drive_h_base_url_direct = "https://drive-h.quark.cn/"
        # 账户信息和会话
        self._accounts_lock = threading.Lock()
        self._accounts = None
        self.session = None
        self.quark_headers = {
            **DEFAULT_HEADERS,
            "Referer": self.quark_base_url,
        }
        logger.info("夸克网盘服务初始化完成")

    async def initialize(self):
        """初始化网盘服务会话"""
        logger.info("初始化夸克网盘服务会话...")
        self.session = httpx.AsyncClient(
            timeout=DEFAULT_TIMEOUT,
            follow_redirects=True,
            http2=True,
            limits=httpx.Limits(
                max_connections=100,
                max_keepalive_connections=50,
            ),
            verify=False,
            transport=httpx.AsyncHTTPTransport(retries=MAX_RETRIES, verify=False),
        )
        # 预加载账户信息
        _ = self.accounts
        logger.info("夸克网盘服务会话初始化完成")

    @property
    def quark_base_url(self):
        """获取夸克网盘主URL"""
        return (
            self.quark_base_url_proxy if self.use_proxy else self.quark_base_url_direct
        )

    @property
    def quark_drive_pc_base_url(self):
        """获取夸克网盘PC端URL"""
        return (
            self.quark_drive_pc_base_url_proxy
            if self.use_proxy
            else self.quark_drive_pc_base_url_direct
        )

    @property
    def quark_drive_h_base_url(self):
        """获取夸克网盘H端URL"""
        return (
            self.quark_drive_h_base_url_proxy
            if self.use_proxy
            else self.quark_drive_h_base_url_direct
        )

    @property
    def accounts(self):
        """获取账户信息"""
        with self._accounts_lock:
            self._accounts = self._load_config_accounts()
            return self._accounts

    def _load_config_accounts(self):
        """从配置加载账户信息"""
        try:
            accounts = {
                "quark_accounts": settings.get("quark_accounts", []),
            }
            return accounts
        except Exception as e:
            logger.error(f"加载账户信息失败: {str(e)}")
            return {"quark_accounts": []}

    def save_accounts(self):
        """保存账户信息"""
        logger.warning(
            "当前账户信息存储在config.yaml，不支持写回。请手动维护config.yaml。"
        )

    async def get_user_info(self, account_index: int = 0) -> Dict[str, Any]:
        """
        获取指定账户的用户信息
        :param account_index: 账户索引
        :return: 用户信息字典
        """
        if (
            not self.accounts.get("quark_accounts")
            or len(self.accounts["quark_accounts"]) <= account_index
        ):
            return {"status": "error", "message": "账户不存在"}
        return await self._get_quark_user_info(
            self.accounts["quark_accounts"][account_index], account_index
        )

    async def _get_quark_user_info(
        self, account: Dict[str, str], account_index: int
    ) -> Dict[str, Any]:
        """
        获取夸克网盘用户信息
        :param account: 账户信息
        :param account_index: 账户索引
        :return: 用户信息字典
        """
        try:
            headers = {
                **self.quark_headers,
                "Cookie": account["cookie"],
            }
            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                # 获取用户基本信息
                url = self.quark_base_url + "account/info"
                response = await client.get(url, headers=headers)
                if response.status_code != 200:
                    logger.error(f"获取夸克网盘用户信息失败: {response.status_code}")
                    return {"status": "error", "message": "获取用户信息失败"}

                data = response.json()
                if data.get("code") != "OK":
                    return {
                        "status": "error",
                        "message": data.get("message", "未知错误"),
                    }

                user_data = data.get("data", {})
                if not user_data:
                    return {"status": "error", "message": "Cookie已失效"}

                # 更新账户信息
                self.accounts["quark_accounts"][account_index]["nickname"] = (
                    user_data.get("nickname", "")
                )

                # 获取存储信息
                capacity_url = self.quark_drive_pc_base_url + "1/clouddrive/member"
                capacity_params = {
                    "pr": "ucpro",
                    "fr": "pc",
                    "uc_param_str": "",
                    "fetch_subscribe": "true",
                    "_ch": "home",
                    "fetch_identity": "true",
                }
                capacity_response = await client.get(
                    capacity_url, params=capacity_params, headers=headers
                )
                capacity_data = capacity_response.json()

                if capacity_data.get("code") == 0:
                    capacity_info = capacity_data.get("data", {})
                    used_size = capacity_info.get("use_capacity", 0)
                    total_size = capacity_info.get("total_capacity", 0)
                    remaining_size = total_size - used_size
                    self.accounts["quark_accounts"][account_index][
                        "remaining_size"
                    ] = remaining_size
                    self.save_accounts()

                return {
                    "status": "success",
                    "username": user_data.get("nickname", "未知用户"),
                    "nickname": user_data.get("nickname", ""),
                }
        except Exception as e:
            logger.error(f"获取夸克网盘用户信息异常: {str(e)}")
            return {"status": "error", "message": f"发生异常: {str(e)}"}

    async def process_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个转存任务
        :param task_data: 任务数据
        :return: 处理结果
        """
        async with self.task_semaphore:
            try:
                share_url = task_data.get("share_url")
                share_pwd = task_data.get("share_pwd")
                account_index = task_data.get("account_index", 0)
                account = self.accounts["quark_accounts"][account_index]
                save_path = task_data.get("save_path", "来自：分享")
                expiry_days = task_data.get("expiry_days", 2)
                enable_rename = task_data.get("enable_rename", True)
                return await self._save_quark_shared_file(
                    share_url, share_pwd, account, save_path, expiry_days, enable_rename
                )
            except Exception as e:
                logger.error(f"处理任务异常: {str(e)}")
                return {"status": "error", "message": f"处理任务异常: {str(e)}"}

    async def save_shared_file(
        self,
        share_url: str,
        share_pwd: Optional[str] = None,
        account_index: int = 0,
        save_path: str = "来自：分享",
        expiry_days: int = 2,
        enable_rename: bool = True,
    ) -> Dict[str, Any]:
        """
        保存夸克网盘分享文件（外部接口）
        :param share_url: 分享链接
        :param share_pwd: 提取码
        :param account_index: 账户索引
        :param save_path: 保存路径
        :param expiry_days: 分享有效期(天)
        :param enable_rename: 是否启用重命名文件引流
        :return: 处理结果
        """
        logger.info(f"开始保存夸克网盘分享文件: {share_url}")
        start_time = time.time()
        task_data = {
            "share_url": share_url,
            "share_pwd": share_pwd,
            "account_index": account_index,
            "save_path": save_path,
            "expiry_days": expiry_days,
            "enable_rename": enable_rename,
        }
        await self.task_queue.put(task_data)
        result = await self.process_task(task_data)
        end_time = time.time()
        logger.info(f"夸克网盘文件保存耗时: {end_time - start_time:.2f}秒")
        return result

    async def check_resource_status(self, share_url: str) -> dict:
        """
        检查夸克网盘资源有效性
        :param share_url: 分享链接
        :return: 检查结果
        """
        if not share_url:
            return {"valid": False, "message": "分享链接为空"}
        try:
            if "pan.quark.cn" not in share_url:
                return {"valid": False, "message": "非夸克网盘链接"}
            return await self._check_quark_link(share_url)
        except Exception as e:
            logger.error(f"检查夸克网盘资源状态异常: {str(e)}")
            return {"valid": False, "message": f"检查异常: {str(e)}"}

    async def _check_quark_link(self, share_url: str) -> dict:
        """
        检查夸克网盘分享链接是否有效
        :param share_url: 分享链接
        :return: 检查结果
        """
        try:
            pwd_id_match = re.search(r"pan\.quark\.cn/s/([a-zA-Z0-9]+)", share_url)
            if not pwd_id_match:
                return {"valid": False, "message": "无法从链接提取夸克网盘ID"}

            pwd_id = pwd_id_match.group(1)
            api_url = self.quark_drive_h_base_url + "1/clouddrive/share/sharepage/token"
            payload = {"pwd_id": pwd_id, "passcode": ""}

            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                response = await client.post(
                    api_url,
                    json=payload,
                    headers={
                        "Content-Type": "application/json",
                        "User-Agent": self.quark_headers["User-Agent"],
                    },
                )

            if response.status_code == 404:
                return {"valid": False, "message": "夸克网盘资源不存在"}
            elif response.status_code == 200:
                return {"valid": True, "message": "夸克网盘链接有效"}
            else:
                data = response.json()
                return {
                    "valid": False,
                    "message": f"夸克网盘请求返回: {response.status_code}, 错误信息: {data.get('message', '')}",
                }
        except Exception as e:
            logger.error(f"检查夸克网盘链接异常: {str(e)}")
            return {"valid": False, "message": f"检查夸克网盘异常: {str(e)}"}

    async def _extract_quark_share_id(self, share_url: str) -> Optional[str]:
        """
        从夸克网盘分享链接中提取分享ID

        Args:
            share_url: 分享链接

        Returns:
            提取的分享ID，如果提取失败返回None
        """
        try:
            share_id_match = re.search(r"pan\.quark\.cn/s/([a-zA-Z0-9]+)", share_url)
            if not share_id_match:
                logger.error("无效的夸克网盘分享链接格式")
                return None

            share_id = share_id_match.group(1)
            logger.info(f"提取到夸克网盘分享ID: {share_id}")
            return share_id
        except Exception as e:
            logger.error(f"提取夸克网盘分享ID异常: {str(e)}")
            return None

    async def _make_request(
        self,
        client: httpx.AsyncClient,
        method: str,
        url: str,
        headers: Dict[str, str],
        **kwargs,
    ) -> Dict[str, Any]:
        """
        统一的HTTP请求处理方法

        Args:
            client: httpx客户端
            method: 请求方法
            url: 请求URL
            headers: 请求头
            **kwargs: 其他请求参数

        Returns:
            Dict[str, Any]: 响应结果
        """
        start_time = time.time()
        try:
            # 发送请求并记录耗时
            response = await client.request(method, url, headers=headers, **kwargs)
            response.raise_for_status()
            result = response.json()

            # 记录耗时较长的请求
            elapsed_time = time.time() - start_time
            if elapsed_time > 1.0:
                logger.warning(f"请求耗时较长: {elapsed_time:.2f}秒, URL: {url}")

            return result
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP状态错误: {e.response.status_code}, URL: {url}")
            return {
                "status": "error",
                "message": f"HTTP状态错误: {e.response.status_code}",
            }
        except httpx.RequestError as e:
            logger.error(f"请求错误: {str(e)}, URL: {url}")
            return {"status": "error", "message": f"请求错误: {str(e)}"}
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {str(e)}, URL: {url}")
            return {"status": "error", "message": f"JSON解析错误: {str(e)}"}
        except Exception as e:
            logger.error(f"请求异常: {str(e)}, URL: {url}")
            return {"status": "error", "message": f"请求异常: {str(e)}"}

    async def _wait_for_task(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        task_id: str,
        task_type: str,
        max_retries: int = 10,
        retry_delay: float = 0.8,
    ) -> Dict[str, Any]:
        """
        统一的等待任务完成方法

        Args:
            client: httpx客户端
            headers: 请求头
            task_id: 任务ID
            task_type: 任务类型描述
            max_retries: 最大重试次数
            retry_delay: 重试间隔时间(秒)

        Returns:
            Dict[str, Any]: 任务结果
        """
        logger.info(f"开始等待{task_type}任务完成，任务ID: {task_id}")
        start_time = time.time()

        for retry_index in range(1, max_retries + 1):
            try:
                task_url = self.quark_drive_pc_base_url + "1/clouddrive/task"
                task_params = {
                    "pr": "ucpro",
                    "fr": "pc",
                    "uc_param_str": "",
                    "task_id": task_id,
                    "retry_index": retry_index,
                    "__dt": random.randint(100, 9999),
                    "__t": int(time.time() * 1000),
                }

                logger.info(
                    f"第 {retry_index} 次检查{task_type}任务状态，任务ID: {task_id}"
                )
                task_response = await client.get(
                    task_url, params=task_params, headers=headers
                )

                if task_response.status_code != 200:
                    logger.warning(
                        f"{task_type}任务状态查询返回非200状态码: {task_response.status_code}"
                    )
                    await asyncio.sleep(retry_delay)
                    continue

                task_result = task_response.json()

                # 检查API返回码
                if task_result.get("code") != 0:
                    logger.warning(
                        f"{task_type}任务状态查询失败，错误码: {task_result.get('code')}"
                    )
                    await asyncio.sleep(retry_delay)
                    continue

                # 获取任务状态
                task_status = task_result.get("data", {}).get("status")

                if task_status == 2:  # 任务完成
                    elapsed_time = time.time() - start_time
                    logger.info(
                        f"{task_type}任务完成，耗时: {elapsed_time:.2f}秒，任务ID: {task_id}"
                    )
                    return {"status": "success", "data": task_result.get("data")}
                elif task_status == 1:  # 任务失败
                    logger.error(f"{task_type}任务失败，任务ID: {task_id}")
                    return {"status": "error", "message": f"{task_type}任务失败"}
                else:  # 任务进行中
                    logger.info(
                        f"{task_type}任务进行中，等待{retry_delay}秒后重试，任务ID: {task_id}"
                    )
                    await asyncio.sleep(retry_delay)

            except Exception as e:
                logger.error(
                    f"检查{task_type}任务状态异常: {str(e)}, 任务ID: {task_id}"
                )
                await asyncio.sleep(retry_delay)

        # 超过最大重试次数
        elapsed_time = time.time() - start_time
        logger.error(
            f"{task_type}任务超时，总耗时: {elapsed_time:.2f}秒，任务ID: {task_id}"
        )
        return {"status": "error", "message": f"{task_type}任务超时"}

    async def _get_quark_share_token(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        share_id: str,
        share_pwd: Optional[str],
    ) -> Dict[str, Any]:
        """
        获取夸克网盘分享token

        Args:
            client: httpx客户端
            headers: 请求头
            share_id: 分享ID
            share_pwd: 提取码

        Returns:
            分享token信息
        """
        try:
            token_url = (
                self.quark_drive_h_base_url + "1/clouddrive/share/sharepage/token"
            )
            token_params = {
                "pr": "ucpro",
                "fr": "pc",
                "uc_param_str": "",
                "__dt": random.randint(100, 9999),
                "__t": int(time.time() * 1000),
            }
            token_data = {
                "pwd_id": share_id,
                "passcode": share_pwd if share_pwd else "",
            }

            token_response = await client.post(
                token_url, params=token_params, json=token_data, headers=headers
            )
            token_result = token_response.json()

            if token_result.get("code") != 0:
                return {"status": "error", "message": "提取码错误或链接失效"}

            share_token = token_result.get("data", {}).get("stoken", "")
            if not share_token:
                return {"status": "error", "message": "获取分享token失败"}

            return {"status": "success", "share_token": share_token}
        except Exception as e:
            logger.error(f"获取夸克网盘分享token异常: {str(e)}")
            return {"status": "error", "message": f"获取分享token异常: {str(e)}"}

    async def _get_quark_share_file_list(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        share_id: str,
        share_token: str,
    ) -> Dict[str, Any]:
        """
        获取夸克网盘分享文件列表

        Args:
            client: httpx客户端
            headers: 请求头
            share_id: 分享ID
            share_token: 分享token

        Returns:
            文件列表信息
        """
        try:
            detail_url = self.quark_base_url + "1/clouddrive/share/sharepage/detail"
            detail_params = {
                "pr": "ucpro",
                "fr": "pc",
                "uc_param_str": "",
                "pwd_id": share_id,
                "stoken": share_token,
                "pdir_fid": "0",
                "force": "0",
                "_page": "1",
                "_size": "50",
                "_fetch_banner": "1",
                "_fetch_share": "1",
                "_fetch_total": "1",
                "_sort": "file_type:asc,file_name:asc",
                "__dt": random.randint(100, 9999),
                "__t": int(time.time() * 1000),
            }

            detail_response = await client.get(
                detail_url, params=detail_params, headers=headers
            )
            detail_data = detail_response.json()

            if detail_data.get("code") != 0:
                return {"status": "error", "message": "获取文件列表失败"}

            file_list = detail_data.get("data", {}).get("list", [])
            if not file_list:
                return {"status": "error", "message": "没有可转存的文件"}
            # 改造这一块
            return {"status": "success", "file_list": file_list}
        except Exception as e:
            logger.error(f"获取夸克网盘分享文件列表异常: {str(e)}")
            return {"status": "error", "message": f"获取文件列表异常: {str(e)}"}

    # 夸克网盘获取文件夹id
    async def get_quark_folder_id(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        dir_name: str,
    ) -> Dict[str, Any]:
        """
        获取夸克网盘 "转存专用" 文件夹ID
        """
        try:
            search_url = self.quark_drive_pc_base_url + "1/clouddrive/file/search"
            search_params = {
                "pr": "ucpro",
                "fr": "pc",
                "uc_param_str": "",
                "q": dir_name,
                "_page": 1,
                "_size": 50,
                "_fetch_total": 1,
                "_sort": "file_type:desc,updated_at:desc",
                "_is_hl": 1,
            }
            search_response = await client.get(
                search_url,
                params=search_params,
                headers=headers,
            )
            search_result = search_response.json()

            if search_result.get("code") != 0:
                return {
                    "status": "error",
                    "message": f"搜索文件夹失败: {search_result.get('message', '未知错误')}",
                }

            file_list = search_result.get("data", {}).get("list", [])
            if not file_list:
                return {"status": "error", "message": "未找到 '转存专用' 文件夹"}

            folder_id = file_list[0].get("fid")
            if not folder_id:
                return {"status": "error", "message": "未能从搜索结果中提取文件夹ID"}

            return {
                "status": "success",
                "folder_id": folder_id,
            }
        except Exception as e:
            logger.error(f"获取夸克网盘文件夹ID异常: {str(e)}")
            return {"status": "error", "message": f"获取文件夹ID异常: {str(e)}"}

    async def _submit_quark_save_task(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        share_id: str,
        share_token: str,
        dir_name: str = "来自：分享",
    ) -> Dict[str, Any]:
        """
        提交夸克网盘转存任务

        Args:
            client: httpx客户端
            headers: 请求头
            share_id: 分享ID
            share_token: 分享token

        Returns:
            转存任务提交结果
        """
        try:
            submit_url = (
                self.quark_drive_pc_base_url + "1/clouddrive/share/sharepage/save"
            )
            submit_params = {
                "pr": "ucpro",
                "fr": "pc",
                "uc_param_str": "",
                "__dt": random.randint(100, 9999),
                "__t": int(time.time() * 1000),
            }

            # 获取目录id
            get_folder_id_result = await self.get_quark_folder_id(
                client, headers, dir_name
            )
            to_pdir_fid = get_folder_id_result.get("folder_id")
            if not to_pdir_fid:
                return {
                    "status": "error",
                    "message": "获取'转存专用'文件夹ID失败，请确保该文件夹存在",
                }

            # 保存到指定目录，这里使用固定目录ID
            submit_data = {
                "fid_list": [],
                "fid_token_list": [],
                "to_pdir_fid": to_pdir_fid,
                "pwd_id": share_id,
                "stoken": share_token,
                "pdir_fid": "0",
                "pdir_save_all": True,
                "exclude_fids": [],
                "scene": "link",
            }

            submit_response = await client.post(
                submit_url, params=submit_params, json=submit_data, headers=headers
            )
            submit_result = submit_response.json()
            logger.info(f"提交转存任务结果: {submit_result}")

            if submit_result.get("code") != 0:
                error_msg = submit_result.get("message", "未知错误")
                return {
                    "status": "error",
                    "message": f"提交转存任务失败: {error_msg}",
                }

            task_id = submit_result.get("data", {}).get("task_id")
            if not task_id:
                return {"status": "error", "message": "获取转存任务ID失败"}

            return {"status": "success", "task_id": task_id}
        except Exception as e:
            logger.error(f"提交夸克网盘转存任务异常: {str(e)}")
            return {"status": "error", "message": f"提交转存任务异常: {str(e)}"}

    async def _wait_quark_save_task(
        self, client: httpx.AsyncClient, headers: Dict[str, str], task_id: str
    ) -> Dict[str, Any]:
        """
        等待夸克网盘转存任务完成

        Args:
            client: httpx客户端
            headers: 请求头
            task_id: 任务ID

        Returns:
            转存任务结果
        """
        result = await self._wait_for_task(
            client=client,
            headers=headers,
            task_id=task_id,
            task_type="转存",
            max_retries=10,
            retry_delay=0.8,
        )

        if result.get("status") != "success":
            return result

        # 解析转存任务特有的数据
        task_data = result.get("data", {})
        task_save_as = task_data.get("save_as", {})
        to_pdir_fid = task_save_as.get("to_pdir_fid")
        save_as_top_fids = task_save_as.get("save_as_top_fids", [])

        if not to_pdir_fid or not save_as_top_fids:
            logger.error(f"获取转存文件信息失败，任务ID: {task_id}")
            return {"status": "error", "message": "获取转存文件信息失败"}

        return {
            "status": "success",
            "to_pdir_fid": to_pdir_fid,
            "save_as_top_fids": save_as_top_fids,
        }

    async def _create_quark_share_task(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        file_ids: list,
        expiry_days: int = 2,
    ) -> Dict[str, Any]:
        """
        创建夸克网盘分享任务

        Args:
            client: httpx客户端
            headers: 请求头
            file_ids: 要分享的文件ID列表

        Returns:
            分享任务创建结果
        """
        try:
            share_url = self.quark_drive_pc_base_url + "1/clouddrive/share"
            share_params = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}

            # 默认分享7天
            share_data = {
                "fid_list": file_ids,
                "title": "分享文件",
                "url_type": 1,
                "expired_type": expiry_days,  # 2是一天有效期，1是永久有效
            }

            share_response = await client.post(
                share_url, params=share_params, json=share_data, headers=headers
            )
            share_result = share_response.json()

            if share_result.get("code") != 0:
                error_msg = share_result.get("message", "未知错误")
                return {
                    "status": "error",
                    "message": f"创建分享失败: {error_msg}",
                }

            share_task_id = share_result.get("data", {}).get("task_id")
            if not share_task_id:
                return {"status": "error", "message": "获取分享任务ID失败"}

            return {"status": "success", "share_task_id": share_task_id}
        except Exception as e:
            logger.error(f"创建夸克网盘分享任务异常: {str(e)}")
            return {"status": "error", "message": f"创建分享任务异常: {str(e)}"}

    async def _wait_quark_share_task(
        self, client: httpx.AsyncClient, headers: Dict[str, str], share_task_id: str
    ) -> Dict[str, Any]:
        """
        等待夸克网盘分享任务完成

        Args:
            client: httpx客户端
            headers: 请求头
            share_task_id: 分享任务ID

        Returns:
            分享任务结果
        """
        result = await self._wait_for_task(
            client=client,
            headers=headers,
            task_id=share_task_id,
            task_type="分享",
            max_retries=10,
            retry_delay=0.8,
        )

        if result.get("status") != "success":
            return result

        # 解析分享任务特有的数据
        share_id = result.get("data", {}).get("share_id")
        if not share_id:
            return {"status": "error", "message": "获取分享ID失败"}

        return {"status": "success", "share_id": share_id}

    # 新建引流文件夹
    async def _create_quark_flow_folder(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        dir_name: str,
        pdir_fid,
    ) -> Dict[str, Any]:
        """
        新建引流文件夹
        """
        try:
            create_folder_url = (
                self.quark_drive_pc_base_url
                + "1/clouddrive/file?pr=ucpro&fr=pc&uc_param_str="
            )
            create_folder_data = {
                "pdir_fid": pdir_fid,
                "file_name": dir_name,
                "dir_path": "",
                "dir_init_lock": False,
            }

            response = await client.post(
                create_folder_url,
                json=create_folder_data,
                headers=headers,
            )
            result = response.json()

            if result.get("code") != 0:
                return {
                    "status": "error",
                    "message": f"创建文件夹失败: {result.get('message', '未知错误')}",
                }

            return {
                "status": "success",
                "data": result.get("data"),
            }
        except Exception as e:
            logger.error(f"创建夸克引流文件夹异常: {str(e)}")
            return {"status": "error", "message": f"创建引流文件夹异常: {str(e)}"}

    async def _rename_quark_file(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        fid: str,
        new_name: str,
    ) -> Dict[str, Any]:
        """
        重命名夸克网盘文件
        """
        try:
            # 获取文件的fid
            fid_result = await self._get_fid_and_name(client, headers, fid)
            if fid_result.get("status") != "success":
                return fid_result
            fid = fid_result.get("fid")
            file_name = fid_result.get("file_name")
            rename_url = self.quark_drive_pc_base_url + "1/clouddrive/file/rename"
            rename_params = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            rename_data = {
                "fid": fid,
                "file_name": new_name + file_name,
            }
            rename_response = await client.post(
                rename_url,
                params=rename_params,
                json=rename_data,
                headers=headers,
            )
            rename_result = rename_response.json()

            if rename_result.get("code") != 0:
                return {
                    "status": "error",
                    "message": f"重命名文件失败: {rename_result.get('message', '未知错误')}",
                }

            return {"status": "success", "data": rename_result.get("data")}
        except Exception as e:
            logger.error(f"重命名夸克网盘文件异常: {str(e)}")
            return {"status": "error", "message": f"重命名文件异常: {str(e)}"}

    async def _get_fid_and_name(
        self, client: httpx.AsyncClient, headers: Dict[str, str], pdir_fid: str
    ) -> Dict[str, Any]:
        """
        获取夸克网盘文件的fid
        """
        try:
            get_fid_url = self.quark_drive_pc_base_url + "1/clouddrive/file/sort"
            params = {
                "pr": "ucpro",
                "fr": "pc",
                "uc_param_str": "",
                "pdir_fid": pdir_fid,
                "_page": "1",
                "_size": "50",
                "_fetch_total": "1",
                "_fetch_sub_dirs": "0",
                "_sort": "file_type:asc,updated_at:desc",
            }
            response = await client.get(get_fid_url, params=params, headers=headers)
            response.raise_for_status()
            result = response.json()

            if result.get("code") != 0:
                return {
                    "status": "error",
                    "message": f"获取fid失败: {result.get('message', '未知错误')}",
                }

            file_list = result.get("data", {}).get("list", [])
            if not file_list:
                return {"status": "error", "message": "文件夹内没有文件"}

            fid = file_list[0].get("fid")
            if not fid:
                return {"status": "error", "message": "未能从结果中提取fid"}

            return {
                "status": "success",
                "fid": fid,
                "file_name": file_list[0].get("file_name"),
            }
        except httpx.HTTPStatusError as e:
            logger.error(
                f"获取夸克网盘文件fid HTTP状态错误: {e.response.status_code}, url: {e.request.url}"
            )
            return {
                "status": "error",
                "message": f"HTTP状态错误: {e.response.status_code}",
            }
        except Exception as e:
            logger.error(f"获取夸克网盘文件fid异常: {str(e)}")
            return {"status": "error", "message": f"获取文件fid异常: {str(e)}"}

    async def _get_quark_share_password(
        self, client: httpx.AsyncClient, headers: Dict[str, str], share_id: str
    ) -> Dict[str, Any]:
        """
        获取夸克网盘分享链接和密码

        Args:
            client: httpx客户端
            headers: 请求头
            share_id: 分享ID

        Returns:
            分享链接和密码信息
        """
        try:
            password_url = self.quark_drive_pc_base_url + "1/clouddrive/share/password"
            password_params = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            password_data = {"share_id": share_id}

            password_response = await client.post(
                password_url,
                params=password_params,
                json=password_data,
                headers=headers,
            )
            password_result = password_response.json()

            if password_result.get("code") != 0:
                error_msg = password_result.get("message", "未知错误")
                return {
                    "status": "error",
                    "message": f"获取分享链接失败: {error_msg}",
                }

            share_info = password_result.get("data", {})
            return {"status": "success", "share_info": share_info}
        except Exception as e:
            logger.error(f"获取夸克网盘分享链接和密码异常: {str(e)}")
            return {"status": "error", "message": f"获取分享链接异常: {str(e)}"}

    async def _get_resource_details_by_folder(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        pwd_id: str,
        stoken: str,
        pdir_fid: str = "0",
        max_depth: int = 2,
        current_depth: int = 0,
    ) -> Dict[str, Any]:
        """
        递归获取夸克网盘文件夹内容

        Args:
            client: httpx客户端
            headers: 请求头
            pwd_id: 分享ID
            stoken: 分享token
            pdir_fid: 父目录ID
            max_depth: 最大递归深度
            current_depth: 当前递归深度

        Returns:
            文件列表及相关信息
        """
        try:
            logger.debug(
                f"获取文件夹内容: pwd_id={pwd_id}, pdir_fid={pdir_fid}, depth={current_depth}/{max_depth}"
            )

            # 构建请求URL和参数
            detail_url = self.quark_base_url + "1/clouddrive/share/sharepage/detail"
            detail_params = {
                "pr": "ucpro",
                "fr": "pc",
                "uc_param_str": "",
                "pwd_id": pwd_id,
                "stoken": stoken,
                "pdir_fid": pdir_fid,
                "force": "0",
                "_page": "1",
                "_size": "100",  # 获取较多文件
                "_fetch_banner": "1",
                "_fetch_share": "1",
                "_fetch_total": "1",
                "_sort": "file_type:asc,file_name:desc",
                "__dt": random.randint(100, 9999),
                "__t": int(time.time() * 1000),
            }

            # 发送请求
            detail_response = await client.get(
                detail_url, params=detail_params, headers=headers
            )
            detail_data = detail_response.json()

            # 检查响应状态
            if detail_data.get("code") != 0:
                logger.error(
                    f"获取文件列表失败: code={detail_data.get('code')}, message={detail_data.get('message')}"
                )
                return {
                    "status": "error",
                    "message": f"获取文件列表失败: {detail_data.get('message', '未知错误')}",
                }

            # 提取文件列表
            file_items = detail_data.get("data", {}).get("list", [])
            share_info = detail_data.get("data", {}).get("share", {})

            logger.debug(
                f"获取到文件列表: 目录ID={pdir_fid}, 文件数量={len(file_items)}"
            )

            # 处理文件列表
            files = []
            dirs = []

            for item in file_items:
                is_dir = item.get("dir", False)
                file_info = {
                    "file_name": item.get("file_name", "未命名"),
                    "fid": item.get("fid", ""),
                    "category": item.get("category", 0),
                    "file_type": item.get("file_type", 0),
                    "size": item.get("size", 0),
                    "format_type": item.get("format_type", ""),
                    "is_dir": is_dir,
                    "updated_at": item.get("last_update_at", 0)
                    or item.get("l_updated_at", 0),
                }

                if is_dir:
                    dirs.append(file_info)
                    logger.debug(
                        f"发现目录: {file_info['file_name']}, fid={file_info['fid']}"
                    )
                else:
                    files.append(file_info)

            # 如果需要递归处理子目录且未超过最大深度
            all_files = files.copy()
            logger.debug(f"当前目录下的文件数: {len(files)}, 子目录数: {len(dirs)}")

            if current_depth < max_depth:
                for dir_item in dirs:
                    # 对每个子目录递归调用
                    sub_dir_fid = dir_item["fid"]
                    logger.debug(
                        f"递归处理子目录: fid={sub_dir_fid}, 当前深度={current_depth}, 最大深度={max_depth}"
                    )

                    sub_result = await self._get_resource_details_by_folder(
                        client=client,
                        headers=headers,
                        pwd_id=pwd_id,
                        stoken=stoken,
                        pdir_fid=sub_dir_fid,
                        max_depth=max_depth,
                        current_depth=current_depth + 1,
                    )

                    if sub_result.get("status") == "success" and "files" in sub_result:
                        # 将子目录文件添加到总文件列表
                        sub_files = sub_result["files"]
                        logger.debug(
                            f"子目录 {dir_item['file_name']} 返回文件数: {len(sub_files)}"
                        )
                        all_files.extend(sub_files)
                    else:
                        logger.warning(
                            f"获取子目录 {dir_item['file_name']} 失败: {sub_result.get('message', '未知错误')}"
                        )
            else:
                logger.debug(f"已达到最大递归深度 {max_depth}，不再处理子目录")

            logger.debug(f"目录处理完成，总文件数: {len(all_files)}")

            return {
                "status": "success",
                "files": all_files,
                "dirs": dirs,
                "share_info": share_info,
            }

        except Exception as e:
            logger.error(f"获取夸克网盘文件夹内容异常: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            return {"status": "error", "message": f"获取文件夹内容异常: {str(e)}"}

    async def _get_resource_details(self, resource: PanResource) -> dict:
        """
        从夸克网盘获取资源详情,智能处理单文件/单文件夹/混合分享等场景

        Args:
            resource: 需要获取详情的资源对象

        Returns:
            dict: 资源详情字典，包含文件列表、分享信息、作者信息等
        """
        logger.info(
            f"开始获取夸克网盘资源详情: 原始链接={resource.original_url}, 资源Key={resource.resource_key}"
        )

        if (
            not self.accounts.get("quark_accounts")
            or not self.accounts["quark_accounts"]
        ):
            logger.error("夸克网盘账户列表为空")
            return {"status": "error", "message": "夸克网盘账户列表为空"}

        account = self.accounts["quark_accounts"][0]
        if not account.get("cookie"):
            logger.error("夸克网盘cookie为空")
            return {"status": "error", "message": "夸克网盘cookie为空"}

        headers = {**self.quark_headers}

        try:
            async with httpx.AsyncClient(
                timeout=30.0, follow_redirects=True, verify=False
            ) as client:
                share_id = await self._extract_quark_share_id(resource.original_url)
                if not share_id:
                    return {"status": "error", "message": "无效的夸克网盘分享链接格式"}

                token_url = (
                    f"{self.quark_drive_h_base_url}1/clouddrive/share/sharepage/token"
                )
                token_params = {
                    "pr": "ucpro",
                    "fr": "pc",
                    "uc_param_str": "",
                    "__dt": random.randint(100, 9999),
                    "__t": int(time.time() * 1000),
                }
                token_data_payload = {
                    "pwd_id": share_id,
                    "passcode": resource.share_pwd or "",
                }

                token_response = await client.post(
                    token_url,
                    params=token_params,
                    json=token_data_payload,
                    headers=headers,
                )
                token_result = token_response.json()

                if token_result.get("code") != 0:
                    return {"status": "error", "message": "提取码错误或链接失效"}

                token_data = token_result.get("data", {})
                share_token = token_data.get("stoken", "")
                author_info = token_data.get("author", {})
                author_name = author_info.get("nick_name", "")
                author_avatar = author_info.get("avatar_url", "")
                title = token_data.get("title", "")
                logger.debug(
                    f"获取到token信息: token={share_token}, author={author_name}, title={title}"
                )

                # 探测根目录, 获取初始文件列表和分享信息
                root_details_result = await self._get_resource_details_by_folder(
                    client=client,
                    headers=headers,
                    pwd_id=share_id,
                    stoken=share_token,
                    pdir_fid="0",
                    max_depth=0,
                )
                if root_details_result.get("status") != "success":
                    return root_details_result

                initial_file_list = root_details_result.get(
                    "files", []
                ) + root_details_result.get("dirs", [])
                share_info = root_details_result.get("share_info", {})
                expired_at = share_info.get("expired_at", 0)

                all_files = []
                # 根据初始文件列表判断分享类型并处理
                if len(initial_file_list) == 1 and not initial_file_list[0].get(
                    "is_dir"
                ):
                    logger.debug("检测到单文件分享")
                    all_files = initial_file_list
                else:
                    logger.debug(
                        f"检测到多文件/文件夹分享, 根目录项数: {len(initial_file_list)}"
                    )
                    files_at_root = [
                        item for item in initial_file_list if not item.get("is_dir")
                    ]
                    all_files.extend(files_at_root)

                    dirs_to_scan = [
                        item for item in initial_file_list if item.get("is_dir")
                    ]
                    for dir_item in dirs_to_scan:
                        logger.debug(f"正在递归扫描文件夹: {dir_item.get('file_name')}")
                        sub_folder_result = await self._get_resource_details_by_folder(
                            client,
                            headers,
                            share_id,
                            share_token,
                            dir_item["fid"],
                            max_depth=3,
                        )
                        if sub_folder_result.get("status") == "success":
                            all_files.extend(sub_folder_result.get("files", []))

                logger.debug(f"获取到所有文件: {len(all_files)}个")

                # 检查文件列表数量，如果小于1则认为是空文件夹
                if len(all_files) < 1:
                    logger.warning(
                        f"检测到空文件夹或文件数量过少: {len(all_files)}个文件"
                    )
                    return {
                        "status": "error",
                        "message": f"分享内容为空文件夹或文件数量过少（{len(all_files)}个文件），无法处理",
                    }

                sorted_files = sorted(
                    all_files, key=lambda x: int(x.get("updated_at", 0)), reverse=True
                )
                top_10_files = sorted_files[:10]
                top_10_filenames = [
                    file.get("file_name", "未命名") for file in top_10_files
                ]
                logger.debug(f"提取前10个文件: {top_10_filenames}")

                # 定义文件类型映射
                file_type_map = {
                    0: "文件夹",
                    1: "视频",
                    2: "音频",
                    3: "图片",
                    4: "文档",
                    5: "应用",
                    6: "压缩包",
                    7: "其他",
                }
                file_type = "其他"
                if top_10_files:
                    from collections import Counter

                    categories = [file.get("category", 7) for file in top_10_files]
                    most_common_category = Counter(categories).most_common(1)[0][0]
                    file_type = file_type_map.get(most_common_category, "其他")

                total_size = sum(int(file.get("size", "0")) for file in all_files)
                human_size = "0B"
                if total_size < 1024:
                    human_size = f"{total_size}B"
                elif total_size < 1024 * 1024:
                    human_size = f"{total_size/1024:.2f}KB"
                elif total_size < 1024 * 1024 * 1024:
                    human_size = f"{total_size/(1024*1024):.2f}MB"
                else:
                    human_size = f"{total_size/(1024*1024*1024):.2f}GB"

                return {
                    "status": "success",
                    "file_list": top_10_filenames,
                    "all_files": sorted_files,
                    "share_id": share_id,
                    "title": title or resource.title,
                    "author_name": author_name,
                    "author_avatar": author_avatar,
                    "file_type": file_type,
                    "share_url": resource.original_url,
                    "file_size": human_size,
                    "updated_at": share_info.get("updated_at", 0),
                    "expired_at": expired_at,
                }

        except Exception as e:
            logger.error(f"获取夸克网盘资源详情异常: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            return {"status": "error", "message": f"获取资源详情异常: {str(e)}"}

    async def fetch_and_update_resource_details(
        self, resource: PanResource, is_parsed: bool = True
    ) -> Dict[str, Any]:
        """
        获取并更新夸克网盘资源的详细信息。
        成功时返回 {"success": True}, 失败时返回 {"success": False, "error": "错误信息"}。
        """
        try:
            # 使用已有的解析逻辑
            details = await self._get_resource_details(resource)

            if not details.get("status") == "success":
                error_msg = details.get("message", "获取资源详情失败")
                logger.error(f"获取资源详情失败: {error_msg}")
                resource.verified_status = "invalid"
                await resource.save(update_fields=["verified_status", "updated_at"])
                return {"success": False, "error": error_msg}

            # 更新PanResource实例
            # 注意：不再强制设置is_parsed=True，保持用户提交时设置的值
            resource.verified_status = "valid"

            # 根据is_parsed参数决定是否填充字段
            if is_parsed:
                # is_parsed=True: 填充完整字段
                resource.share_url = resource.original_url

                # 更新资源标题 - 检查是否为用户自定义标题
                old_title = resource.title
                if old_title and not old_title.startswith("待解析资源:"):
                    logger.info(f"保持用户自定义标题不变: {old_title}")
                else:
                    resource.title = (
                        details.get("title", resource.title) or resource.title
                    )
                    logger.info(f"更新标题: {old_title} -> {resource.title}")

                resource.author = details.get("author_name", resource.author)
                resource.author_avatar = details.get(
                    "author_avatar", resource.author_avatar
                )
                resource.text_content = (
                    "\n".join(details.get("file_list", []))
                    if details.get("file_list")
                    else "未获取到文件内容"
                )
                resource.file_type = details.get("file_type", "其他")
                resource.file_size = details.get("file_size")

                expired_at_ms = details.get("expired_at", 0)
                if (
                    expired_at_ms
                    and expired_at_ms > 0
                    and expired_at_ms < 7258089600000
                ):  # 小于 2200 年
                    resource.expiry_date = datetime.fromtimestamp(expired_at_ms / 1000)
                    logger.debug(f"设置过期时间: {resource.expiry_date}")

                logger.info(f"is_parsed=True: 填充完整字段，author={resource.author}")
            else:
                # is_parsed=False: 仅填充基本字段，author设为97_bot
                resource.author = "97_bot"

                # 更新资源标题 - 检查是否为用户自定义标题
                old_title = resource.title
                if old_title and not old_title.startswith("待解析资源:"):
                    logger.info(f"is_parsed=False: 保持用户自定义标题不变: {old_title}")
                else:
                    resource.title = (
                        details.get("title", resource.title) or resource.title
                    )
                    logger.info(
                        f"is_parsed=False: 更新标题: {old_title} -> {resource.title}"
                    )

                resource.text_content = (
                    "\n".join(details.get("file_list", []))
                    if details.get("file_list")
                    else "未获取到文件内容"
                )
                resource.file_type = details.get("file_type", "其他")
                resource.file_size = details.get("file_size")
                # 不填充share_url, expiry_date和author_avatar
                logger.info(f"is_parsed=False: 仅填充基本字段，author=97_bot")

            all_files_details = details.get("all_files", [])
            if all_files_details:
                latest_mtime_ms = int(all_files_details[0].get("updated_at", 0))
                if latest_mtime_ms > 0:
                    resource.updated_at = datetime.fromtimestamp(latest_mtime_ms / 1000)
            else:
                resource.updated_at = datetime.now()

            if resource.created_at is None:
                resource.created_at = datetime.now(BEIJING_TIMEZONE)

            logger.debug("继续执行入库...")
            # 如果没有重复，继续执行后续的保存和更新逻辑...
            logger.info(f"开始保存资源到数据库: ID={resource.id}")
            await resource.save()

            return {"success": True}

        except Exception as e:
            logger.error(
                f"处理夸克资源时发生意外错误 (URL: {resource.original_url}): {e}",
                exc_info=True,
            )
            resource.verified_status = "error"
            await resource.save(update_fields=["verified_status", "updated_at"])
            return {"success": False, "error": f"处理时发生内部错误: {str(e)}"}

    async def _save_quark_shared_file(
        self,
        share_url: str,
        share_pwd: Optional[str],
        account: Dict[str, str],
        save_path: str = "来自：分享",
        expiry_days: int = 2,
        enable_rename: bool = True,
    ) -> Dict[str, Any]:
        """
        保存夸克网盘分享文件

        Args:
            share_url: 分享链接
            share_pwd: 提取码
            account: 账户信息
            save_path: 保存路径
            expiry_days: 分享有效期(天)
            enable_rename: 是否启用重命名文件引流

        Returns:
            转存和分享结果
        """
        logger.info(f"开始处理夸克网盘分享文件: {share_url}")
        total_start = time.time()
        try:
            headers = {
                **self.quark_headers,
                "Cookie": account["cookie"],
            }

            async with httpx.AsyncClient(
                timeout=30.0, follow_redirects=True, verify=False
            ) as client:
                # 1. 提取分享ID
                step_start = time.time()
                share_id = await self._extract_quark_share_id(share_url)
                logger.debug(f"步骤1-提取分享ID 耗时: {time.time() - step_start:.2f}秒")

                if not share_id:
                    return {"status": "error", "message": "无效的夸克网盘分享链接格式"}

                # 2. 获取分享token
                step_start = time.time()
                token_result = await self._get_quark_share_token(
                    client, headers, share_id, share_pwd
                )
                logger.debug(
                    f"步骤2-获取分享token 耗时: {time.time() - step_start:.2f}秒"
                )

                if token_result.get("status") == "error":
                    return token_result

                share_token = token_result.get("share_token")

                # 3. 获取文件列表
                step_start = time.time()
                file_list_result = await self._get_quark_share_file_list(
                    client, headers, share_id, share_token
                )
                logger.debug(
                    f"步骤3-获取文件列表 耗时: {time.time() - step_start:.2f}秒"
                )

                if file_list_result.get("status") == "error":
                    return file_list_result

                file_list = file_list_result.get("file_list")

                # 4. 提交转存任务
                step_start = time.time()
                submit_result = await self._submit_quark_save_task(
                    client, headers, share_id, share_token, save_path
                )
                logger.debug(
                    f"步骤4-提交转存任务 耗时: {time.time() - step_start:.2f}秒"
                )

                if submit_result.get("status") == "error":
                    return submit_result

                task_id = submit_result.get("task_id")

                # 5. 等待转存完成
                step_start = time.time()
                task_result = await self._wait_quark_save_task(client, headers, task_id)
                logger.debug(
                    f"步骤5-等待转存完成 耗时: {time.time() - step_start:.2f}秒"
                )

                if task_result.get("status") == "error":
                    return task_result

                to_pdir_fid = task_result.get("to_pdir_fid")
                save_as_top_fids = task_result.get("save_as_top_fids")

                # 6. 创建分享
                step_start = time.time()
                share_result = await self._create_quark_share_task(
                    client, headers, save_as_top_fids, expiry_days
                )
                logger.debug(f"步骤6-创建分享 耗时: {time.time() - step_start:.2f}秒")

                if share_result.get("status") == "error":
                    return share_result

                share_task_id = share_result.get("share_task_id")

                # 7. 等待分享任务完成
                step_start = time.time()
                share_task_result = await self._wait_quark_share_task(
                    client, headers, share_task_id
                )
                logger.debug(
                    f"步骤7-等待分享任务 耗时: {time.time() - step_start:.2f}秒"
                )

                if share_task_result.get("status") == "error":
                    return share_task_result

                share_id = share_task_result.get("share_id")

                # 8. 重命名文件作为引流方式
                if enable_rename:
                    step_start = time.time()
                    rename_result = await self._rename_quark_file(
                        client,
                        headers,
                        save_as_top_fids[0],
                        "【更多资源访问Pansoo.cn】",
                    )
                    logger.debug(f"重命名文件结果: {rename_result}")
                    logger.debug(
                        f"步骤8-重命名文件作为引流方式 耗时: {time.time() - step_start:.2f}秒"
                    )
                else:
                    logger.debug("跳过文件重命名引流步骤")

                # 9. 获取分享链接和密码
                step_start = time.time()
                password_result = await self._get_quark_share_password(
                    client, headers, share_id
                )
                logger.debug(
                    f"步骤9-获取分享密码 耗时: {time.time() - step_start:.2f}秒"
                )

                if password_result.get("status") == "error":
                    return password_result

                share_info = password_result.get("share_info")

                # 总耗时统计
                total_time = time.time() - total_start
                logger.info(f"夸克网盘文件保存总耗时: {total_time:.2f}秒")

                return {
                    "status": "success",
                    "message": "文件已成功转存并分享",
                    "share_url": share_info.get("share_url"),
                    "share_pwd": share_info.get("pwd_id"),
                    "task_id": task_id,
                    "to_pdir_fid": to_pdir_fid,
                    "share_id": share_id,
                    "time_usage": {"total": total_time},
                }

        except Exception as e:
            logger.error(f"保存夸克网盘分享文件异常: {str(e)}")
            return {"status": "error", "message": f"发生异常: {str(e)}"}


# 创建单例实例
def get_quark_pan_service(use_proxy: bool = False):
    return QuarkPanService(use_proxy=use_proxy)


quark_pan_service = get_quark_pan_service()

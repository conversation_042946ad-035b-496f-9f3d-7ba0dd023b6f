import asyncio
import datetime
import traceback
import httpx
import logging
import time
import re
import random
import string
from typing import Dict, Optional, Any, List
from contextlib import asynccontextmanager
from app.models.resource import PanResource
from app.utils.config import settings
import threading
import json
from app.utils.pan_url_parser import parse_pan_url
from tortoise.expressions import Q
from datetime import datetime, timedelta, timezone
from app.services.proxy_service import proxy_service

logger = logging.getLogger("baidu-pan-service")

# 定义北京时间时区（UTC+8）
BEIJING_TIMEZONE = timezone(timedelta(hours=8))

# 从配置文件加载百度网盘服务相关配置
PAN_CONF = settings.get("pan_service", {})
DEFAULT_TIMEOUT = PAN_CONF.get("default_timeout", 30.0)
MAX_RETRIES = PAN_CONF.get("max_retries", 3)
RETRY_DELAY = PAN_CONF.get("retry_delay", 1.0)
MAX_CONCURRENT_TASKS = PAN_CONF.get("max_concurrent_tasks", 5)
DEFAULT_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}


@asynccontextmanager
async def get_client(use_proxy: bool = False):
    """
    获取共享的HTTP客户端的上下文管理器

    参数:
        use_proxy: 是否使用代理

    返回:
        httpx.AsyncClient: 配置好的HTTP客户端
    """
    if use_proxy and proxy_service.is_enabled():
        client = await proxy_service.create_proxy_client(base_timeout=DEFAULT_TIMEOUT)
    else:
        client = httpx.AsyncClient(
            timeout=DEFAULT_TIMEOUT,
            follow_redirects=True,
            http2=True,
            limits=httpx.Limits(
                max_connections=100,
                max_keepalive_connections=50,
            ),
            verify=False,
            transport=httpx.AsyncHTTPTransport(retries=MAX_RETRIES, verify=False),
        )
    try:
        yield client
    finally:
        await client.aclose()


class ConnectionPool:
    """
    HTTP连接池，提高性能并控制并发请求数
    """

    def __init__(self, max_size: int = 10):
        """
        初始化连接池

        参数:
            max_size: 最大连接数量
        """
        self.pool = []
        self.max_size = max_size
        self.semaphore = asyncio.Semaphore(max_size)

    async def get_client(self):
        """
        从连接池获取客户端，如果池中没有可用客户端则创建新的

        返回:
            httpx.AsyncClient: HTTP客户端
        """
        async with self.semaphore:
            if self.pool:
                return self.pool.pop()
            return await self._create_client()

    async def _create_client(self):
        """
        创建新的HTTP客户端

        返回:
            httpx.AsyncClient: 新创建的HTTP客户端
        """
        return httpx.AsyncClient(
            timeout=DEFAULT_TIMEOUT,
            follow_redirects=True,
            http2=True,
            limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
            verify=False,
        )

    async def release_client(self, client):
        """
        将客户端释放回连接池，如果池已满则关闭客户端

        参数:
            client: 要释放的HTTP客户端
        """
        if len(self.pool) < self.max_size:
            self.pool.append(client)
        else:
            await client.aclose()


# 创建全局连接池实例
connection_pool = ConnectionPool(max_size=MAX_CONCURRENT_TASKS)


class BaiduPanService:
    """
    百度网盘服务类

    提供百度网盘的各种操作功能，包括：
    - 用户信息获取
    - 文件转存（默认使用第一个账号，索引0）
    - 分享创建
    - 资源详情获取（使用第二个账号，索引1）

    注意：需要配置至少2个百度网盘账户以支持所有功能
    """

    def __init__(self, use_proxy: bool = False):
        """
        初始化百度网盘服务

        参数:
            use_proxy: 是否使用代理
        """
        logger.info("初始化百度网盘服务...")
        # 基本配置
        self.use_proxy = use_proxy
        self.task_queue = asyncio.Queue()
        self.task_semaphore = asyncio.Semaphore(MAX_CONCURRENT_TASKS)

        # 从配置文件获取代理相关设置
        self.use_proxy_for_parsing = PAN_CONF.get("use_proxy_for_parsing", False)

        # 接口URL配置
        self.baidu_base_url_proxy = "https://14.103.234.187/baidu/"
        self.baidu_base_url_direct = "https://pan.baidu.com/"

        # 账户相关
        self._accounts_lock = threading.Lock()
        self._accounts = None
        self.session = None

        # 通用请求头
        self.baidu_headers = {
            **DEFAULT_HEADERS,
            "Referer": self.baidu_base_url,
        }
        logger.info(
            f"百度网盘服务初始化完成，代理配置: 解析={self.use_proxy_for_parsing}"
        )

    async def initialize(self):
        """
        初始化服务会话和预加载账户信息
        """
        logger.info("初始化百度网盘服务会话...")
        self.session = httpx.AsyncClient(
            timeout=DEFAULT_TIMEOUT,
            follow_redirects=True,
            http2=True,
            limits=httpx.Limits(
                max_connections=100,
                max_keepalive_connections=50,
            ),
            verify=False,
            transport=httpx.AsyncHTTPTransport(retries=MAX_RETRIES, verify=False),
        )
        # 预加载账户
        _ = self.accounts
        logger.info("百度网盘服务会话初始化完成")

    @property
    def baidu_base_url(self):
        """
        获取百度网盘基础URL

        根据是否使用代理返回对应的URL

        返回:
            str: 百度网盘基础URL
        """
        return (
            self.baidu_base_url_proxy if self.use_proxy else self.baidu_base_url_direct
        )

    @property
    def accounts(self):
        """
        获取账户信息

        返回:
            dict: 账户信息字典
        """
        with self._accounts_lock:
            self._accounts = self._load_config_accounts()
            return self._accounts

    def _load_config_accounts(self):
        """
        从配置加载账户信息

        返回:
            dict: 账户信息字典
        """
        try:
            accounts = {
                "baidu_accounts": settings.get("baidu_accounts", []),
            }
            return accounts
        except Exception as e:
            logger.error(f"加载账户信息失败: {str(e)}")
            return {"baidu_accounts": []}

    def save_accounts(self):
        """保存账户信息到配置中"""
        logger.warning(
            "当前账户信息存储在config.yaml，不支持写回。请手动维护config.yaml。"
        )

    async def get_user_info(self, account_index: int = 0) -> Dict[str, Any]:
        """
        获取指定账户的用户信息

        参数:
            account_index: 账户索引

        返回:
            Dict[str, Any]: 用户信息字典，包含状态和详细信息
        """
        if (
            not self.accounts.get("baidu_accounts")
            or len(self.accounts["baidu_accounts"]) <= account_index
        ):
            return {"status": "error", "message": "账户不存在"}
        return await self._get_baidu_user_info(
            self.accounts["baidu_accounts"][account_index], account_index
        )

    async def _get_baidu_user_info(
        self, account: Dict[str, str], account_index: int
    ) -> Dict[str, Any]:
        """
        获取百度网盘用户信息

        参数:
            account: 账户信息字典
            account_index: 账户索引

        返回:
            Dict[str, Any]: 用户信息字典，包含状态和详细信息
        """
        try:
            headers = {
                **self.baidu_headers,
                "Cookie": account["cookie"],
            }

            async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
                # 1. 获取用户基本信息
                url = (
                    self.baidu_base_url
                    + "rest/2.0/membership/user/info?method=query&clienttype=0&app_id=250528&web=1"
                )
                response = await client.get(url, headers=headers)
                if response.status_code != 200:
                    logger.error(f"获取百度网盘用户信息失败: {response.status_code}")
                    return {"status": "error", "message": "获取用户信息失败"}

                data = response.json()
                if data.get("error_code") != 0:
                    return {
                        "status": "error",
                        "message": data.get("error_msg", "未知错误"),
                    }

                user_info = data.get("user_info", {})
                if user_info.get("loginstate") != 1:
                    return {"status": "error", "message": "Cookie已失效"}

                # 2. 更新账户信息
                self.accounts["baidu_accounts"][account_index]["username"] = (
                    user_info.get("username", "")
                )
                self.accounts["baidu_accounts"][account_index]["phone"] = user_info.get(
                    "phone", ""
                )

                # 3. 获取存储配额信息
                quota_url = self.baidu_base_url + "api/quota"
                quota_response = await client.get(quota_url, headers=headers)
                quota_data = quota_response.json()

                if quota_data.get("errno") == 0:
                    used_size = quota_data.get("used", 0)
                    total_size = quota_data.get("total", 0)
                    remaining_size = total_size - used_size
                    self.accounts["baidu_accounts"][account_index][
                        "remaining_size"
                    ] = remaining_size
                    self.save_accounts()

                return {
                    "status": "success",
                    "username": user_info.get("username", "未知用户"),
                    "nickname": user_info.get("username", ""),
                }
        except Exception as e:
            logger.error(f"获取百度网盘用户信息异常: {str(e)}")
            return {"status": "error", "message": f"发生异常: {str(e)}"}

    async def process_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个转存任务

        参数:
            task_data: 任务数据字典，包含分享链接、提取码和账户索引

        返回:
            Dict[str, Any]: 处理结果
        """
        async with self.task_semaphore:
            try:
                share_url = task_data.get("share_url") or ""
                share_pwd = task_data.get("share_pwd")
                account_index = task_data.get("account_index", 0)
                save_path = task_data.get("save_path", "/我的资源")
                expiry_days = task_data.get("expiry_days", 1)
                enable_rename = task_data.get("enable_rename", True)

                # 确保账户索引有效
                if (
                    not self.accounts.get("baidu_accounts")
                    or len(self.accounts["baidu_accounts"]) <= account_index
                ):
                    return {
                        "status": "error",
                        "message": f"账户索引{account_index}不存在",
                    }

                account = self.accounts["baidu_accounts"][account_index]
                logger.info(
                    f"使用账户索引{account_index}进行转存: {account.get('username', '未知用户')}"
                )
                return await self.save_baidu_shared_file(
                    share_url, share_pwd, account, save_path, expiry_days, enable_rename
                )
            except Exception as e:
                logger.error(f"处理任务异常: {str(e)}")
                return {"status": "error", "message": f"处理任务异常: {str(e)}"}

    async def save_shared_file(
        self,
        share_url: str,
        share_pwd: Optional[str] = None,
        account_index: int = 0,
        save_path: str = "/我的资源",
        expiry_days: int = 1,
        enable_rename: bool = True,
    ) -> Dict[str, Any]:
        """
        保存百度网盘分享文件（外部接口）

        参数:
            share_url: 分享链接
            share_pwd: 提取码
            account_index: 账户索引，默认使用第一个账号（索引0）
            save_path: 保存路径
            expiry_days: 分享有效期(天)
            enable_rename: 是否启用引流文件夹创建

        返回:
            Dict[str, Any]: 处理结果
        """
        logger.info(
            f"开始保存百度网盘分享文件: {share_url}，使用账户索引: {account_index}"
        )
        start_time = time.time()

        # 构建任务数据
        task_data = {
            "share_url": share_url,
            "share_pwd": share_pwd,
            "account_index": account_index,  # 默认为0，使用第一个账号
            "save_path": save_path,
            "expiry_days": expiry_days,
            "enable_rename": enable_rename,
        }

        # 将任务加入队列并处理
        await self.task_queue.put(task_data)
        result = await self.process_task(task_data)

        # 记录处理耗时
        end_time = time.time()
        logger.info(f"百度网盘文件保存耗时: {end_time - start_time:.2f}秒")
        return result

    @staticmethod
    def update_cookie(bdclnd: str, cookie: str) -> str:
        """
        更新 cookie 字符串中的 BDCLND 值

        参数:
            bdclnd: 新的 BDCLND 值
            cookie: 原始 cookie 字符串

        返回:
            str: 更新后的 cookie 字符串
        """
        # 将cookie字符串转换为字典
        cookies_dict = {}
        for item in filter(None, cookie.split(";")):
            if "=" in item:
                parts = item.split("=", 1)
                if len(parts) == 2:
                    key, value = parts
                    cookies_dict[key.strip()] = value.strip()

        # 更新BDCLND值 - 注意这里键名应该是"BDCLND"而不是" BDCLND"
        cookies_dict["BDCLND"] = bdclnd

        # 将字典转换回cookie字符串
        updated_cookie = ";".join(
            [f"{key}={value}" for key, value in cookies_dict.items()]
        )
        return updated_cookie

    async def check_resource_status(self, share_url: str) -> dict:
        """
        检查百度网盘资源有效性

        参数:
            share_url: 分享链接

        返回:
            dict: 检查结果，包含valid字段和message字段
        """
        if not share_url:
            return {"valid": False, "message": "分享链接为空"}

        try:
            if "pan.baidu.com" not in share_url:
                return {"valid": False, "message": "非百度网盘链接"}

            return await self._check_baidu_link(share_url)
        except Exception as e:
            logger.error(f"检查百度网盘资源状态异常: {str(e)}")
            return {"valid": False, "message": f"检查异常: {str(e)}"}

    async def _check_baidu_link(self, share_url: str) -> dict:
        """
        检查百度网盘分享链接是否有效

        参数:
            share_url: 分享链接

        返回:
            dict: 检查结果，包含valid字段和message字段
        """
        try:
            async with httpx.AsyncClient(
                timeout=10.0, verify=False, follow_redirects=True
            ) as client:
                # 发送请求获取页面
                response = await client.get(
                    share_url,
                    headers={"User-Agent": self.baidu_headers["User-Agent"]},
                )
                final_url = str(response.url)
                logger.info(f"百度网盘链接检查最终URL: {final_url}")

                # 检查URL是否被重定向到错误页面
                if "error.html" in final_url or "activation.html" in final_url:
                    return {"valid": False, "message": "链接无效或已失效"}

                # 检查页面内容中的关键信息
                page_content = response.text
                if (
                    "啊哦，你来晚了，分享的文件已经被删除了，下次要早点哟"
                    in page_content
                    or "链接不存在" in page_content
                ):
                    return {"valid": False, "message": "分享链接已失效"}

                # 检查页面标题
                title_match = re.search(r"<title>(.*?)</title>", page_content)
                if title_match:
                    title = title_match.group(1)
                    if "百度网盘-链接不存在" in title or "百度网盘-错误页" in title:
                        return {"valid": False, "message": "链接不存在"}

                # 检查HTTP状态码
                if response.status_code == 200:
                    return {"valid": True, "message": "链接有效"}
                else:
                    logger.warning(
                        f"百度网盘链接返回非200状态码: {response.status_code}, URL: {share_url}"
                    )
                    return {
                        "valid": True,
                        "message": f"链接可能有效，状态码: {response.status_code}",
                    }
        except Exception as e:
            logger.error(f"检查百度网盘链接异常: {str(e)}")
            return {"valid": False, "message": f"链接异常: {str(e)}"}

    async def _extract_baidu_share_id(
        self, share_url: str, share_pwd: Optional[str] = None
    ) -> Optional[Dict[str, str]]:
        """
        从百度网盘分享链接中提取分享ID和提取码

        参数:
            share_url: 分享链接
            share_pwd: 提取码（可选）

        返回:
            Optional[Dict[str, str]]: 包含surl和pwd的字典，提取失败返回None
        """
        try:
            # 1. 提取分享ID
            surl_match = re.search(r"pan\.baidu\.com/s/([a-zA-Z0-9_-]+)", share_url)
            if not surl_match:
                logger.error("无效的百度网盘分享链接格式")
                return None

            surl = surl_match.group(1)
            logger.info(f"提取到百度网盘分享ID: {surl}")

            # 2. 尝试从链接中提取提取码
            pwd = share_pwd
            if not pwd:
                pwd_match = re.search(r"pwd=([a-zA-Z0-9]+)", share_url)
                if pwd_match:
                    pwd = pwd_match.group(1)
                    logger.info(f"从链接中提取到提取码: {pwd}")

            return {"surl": surl, "pwd": pwd}
        except Exception as e:
            logger.error(f"提取百度网盘分享ID异常: {str(e)}")
            return None

    async def save_baidu_shared_file(
        self,
        share_url: str,
        share_pwd: Optional[str],
        account: Dict[str, str],
        save_path: str = "/我的资源",
        expiry_days: int = 1,
        enable_rename: bool = True,
    ) -> Dict[str, Any]:
        """
        保存百度网盘分享文件

        参数:
            share_url: 分享链接
            share_pwd: 提取码
            account: 账户信息
            save_path: 保存路径
            expiry_days: 分享有效期(天)
            enable_rename: 是否启用引流文件夹创建

        返回:
            Dict[str, Any]: 转存结果
        """
        logger.info(f"开始处理百度网盘分享文件: {share_url}")
        total_start = time.time()

        try:
            headers = {
                **self.baidu_headers,
                "Cookie": account["cookie"],
                "Accept": "*/*",
                "Host": "pan.baidu.com",
                "Connection": "keep-alive",
            }

            # 使用共享HTTP客户端提高性能
            async with get_client() as client:
                # 步骤1: 从分享链接中提取surl和提取码
                step_start = time.time()
                share_info = await self._extract_baidu_share_id(share_url, share_pwd)
                if not share_info:
                    return {"status": "error", "message": "无效的分享链接格式"}
                logger.info(
                    f"步骤1-提取分享信息 耗时: {time.time() - step_start:.2f}秒"
                )

                # 步骤2: 获取bdstoken和验证分享链接
                step_start = time.time()
                bdstoken_result = await self._get_baidu_bdstoken(client, headers)
                if bdstoken_result.get("status") != "success":
                    return bdstoken_result

                bdstoken = bdstoken_result.get("bdstoken")
                logger.info(
                    f"bdstoken: #{bdstoken}, surl: {share_info}, pwd: {share_info['pwd']}"
                )

                # 如果有提取码，需要验证分享链接
                if share_info["pwd"]:
                    verify_result = await self._verify_baidu_share(
                        client,
                        headers,
                        share_info["surl"],
                        share_info["pwd"],  # bdstoken
                    )
                    logger.info(
                        f"步骤2-获取bdstoken和验证分享链接 耗时: {time.time() - step_start:.2f}秒"
                    )

                    # 检查verify结果
                    if verify_result.get("status") != "success":
                        return verify_result

                    # 更新cookie中的randsk
                    if verify_result.get("randsk"):
                        headers["Cookie"] = self.update_cookie(
                            verify_result.get("randsk"), headers["Cookie"]
                        )

                # 步骤3: 获取分享文件列表
                step_start = time.time()
                list_result = await self._get_baidu_share_list(
                    client, headers, share_info["surl"]
                )
                logger.info(
                    f"步骤3-获取分享文件列表 耗时: {time.time() - step_start:.2f}秒"
                )

                if list_result.get("status") != "success":
                    return list_result

                file_list = list_result.get("file_list")
                share_id = list_result.get("share_id")
                user_id = list_result.get("user_id")
                is_single_file = len(file_list) == 1

                # 步骤4: 转存文件
                step_start = time.time()
                fs_id_list = [str(item["fs_id"]) for item in file_list]
                save_path = save_path

                transfer_result = await self._transfer_baidu_files(
                    client,
                    headers,
                    share_id,
                    user_id,
                    fs_id_list,
                    save_path,
                    bdstoken,
                    is_single_file=is_single_file,
                )
                logger.info(f"步骤4-转存文件 耗时: {time.time() - step_start:.2f}秒")

                if transfer_result.get("status") != "success":
                    return transfer_result

                to_fs_ids = transfer_result.get("to_fs_ids")
                to_save_path = transfer_result.get("to")
                logger.info(f"转存文件ID列表: {to_fs_ids}, 转存路径: {to_save_path}")

                # 步骤5: 创建引流文件夹（根据enable_rename参数决定是否创建）
                if enable_rename:
                    step_start = time.time()
                    await self._create_baidu_flow_folder(
                        client,
                        headers,
                        bdstoken,
                        to_save_path,
                        "更多资源访问【Pansoo.cn】",
                    )
                    logger.info(
                        f"步骤5-创建引流文件夹 耗时: {time.time() - step_start:.2f}秒"
                    )
                else:
                    logger.debug("跳过创建引流文件夹步骤")

                # 步骤6: 创建分享链接
                step_start = time.time()
                share_result = await self._create_baidu_share(
                    to_fs_ids,
                    bdstoken,
                    headers,
                    is_single_file=is_single_file,
                    expiry_days=expiry_days,
                )
                logger.info(
                    f"步骤5-创建分享链接 耗时: {time.time() - step_start:.2f}秒"
                )

                if share_result.get("status") != "success":
                    return {
                        "status": "warning",
                        "message": "文件已保存但创建分享失败",
                        "file_path": save_path,
                    }

                # 总耗时统计
                total_time = time.time() - total_start
                logger.info(f"百度网盘文件保存总耗时: {total_time:.2f}秒")

                # 返回成功结果
                return {
                    "status": "success",
                    "message": "文件已成功转存并分享",
                    "share_url": share_result.get("share_url"),
                    "file_path": save_path,
                    "file_list": [item["filename"] for item in file_list],
                    "time_usage": {"total": total_time},
                }

        except Exception as e:
            logger.error(f"保存百度网盘分享文件异常: {str(e)}")
            return {"status": "error", "message": f"发生异常: {str(e)}"}

    async def _get_baidu_bdstoken(
        self, client: httpx.AsyncClient, headers: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        获取百度网盘 bdstoken

        参数:
            client: httpx 客户端
            headers: 请求头

        返回:
            Dict[str, Any]: 包含bdstoken的结果字典
        """
        try:
            response = await client.get(
                self.baidu_base_url + "api/loginStatus?clienttype=0&web=1",
                headers=headers,
            )
            bdstoken_data = response.json()
            bdstoken = bdstoken_data.get("login_info", {}).get("bdstoken", "")

            if not bdstoken:
                return {
                    "status": "error",
                    "message": "获取bdstoken失败，请检查Cookie是否有效",
                }

            return {"status": "success", "bdstoken": bdstoken}
        except Exception as e:
            logger.error(f"获取bdstoken异常: {str(e)}")
            return {"status": "error", "message": f"获取bdstoken异常: {str(e)}"}

    async def _verify_baidu_share(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        surl: str,
        share_pwd: Optional[str],
        bdstoken: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        验证百度网盘分享链接

        参数:
            client: httpx 客户端
            headers: 请求头
            surl: 分享ID
            share_pwd: 提取码
            bdstoken: bdstoken

        返回:
            Dict[str, Any]: 验证结果，成功时包含randsk
        """
        try:
            # 构建验证URL
            bdstoken = bdstoken or ""
            verify_url = f"{self.baidu_base_url}share/verify?t={int(time.time()*1000)}&surl={surl[1:]}&channel=chunlei&web=1&bdstoken={bdstoken}"
            logger.info(f"验证百度网盘分享链接: {verify_url}")
            # logger.info(f"验证请求头信息: {headers}")

            # 准备验证数据
            verify_data = {
                "pwd": share_pwd if share_pwd else "",
                "vcode": "",
                "vcode_str": "",
            }

            # 发送验证请求
            verify_response = await client.post(
                verify_url, data=verify_data, headers=headers
            )

            # 打印验证结果 html 内容
            logger.info(f"验证结果: {verify_response.text}")

            verify_result = verify_response.json()

            # 检查验证结果
            if verify_result.get("errno") != 0:
                return {
                    "status": "error",
                    "message": f"验证分享链接失败: {verify_result.get('errno', '未知错误')}",
                }

            return {"status": "success", "randsk": verify_result.get("randsk", "")}
        except Exception as e:
            logger.error(f"验证百度网盘分享链接异常: {str(e)}")
            return {"status": "error", "message": f"验证分享链接异常: {str(e)}"}

    async def _get_baidu_share_list(
        self, client: httpx.AsyncClient, headers: Dict[str, str], surl: str
    ) -> Dict[str, Any]:
        """
        获取百度网盘分享文件列表

        参数:
            client: httpx 客户端
            headers: 请求头
            surl: 分享链接的资源标识

        返回:
            Dict[str, Any]: 包含文件列表、分享ID和用户ID的结果字典
        """
        logger.info(f"开始获取百度网盘分享文件列表,原始链接: {surl}")
        try:
            # 构建列表请求URL
            list_url = f"{self.baidu_base_url}share/list?web=5&app_id=250528&desc=1&showempty=0&page=1&num=20&order=time&shorturl={surl[1:]}&root=1&view_mode=1&channel=chunlei&web=1"
            logger.info(f"share/list请求URL: {list_url}")
            # logger.info(f"share/list请求头信息: {headers}")

            # 发送请求获取文件列表
            list_response = await client.get(list_url, headers=headers)
            list_data = list_response.json()

            # 检查响应状态
            if list_data.get("errno") != 0:
                return {
                    "status": "error",
                    "message": f"获取分享信息失败，错误码: {list_data.get('errno')}",
                }

            # 使用get方法并提供默认值，确保不会返回None
            share_id = list_data.get("share_id", "")
            user_id = list_data.get("uk", "")

            if not share_id or not user_id:
                return {"status": "error", "message": "获取分享ID或用户ID失败"}

            # 确保list_data.get("list")返回列表，即使API返回None
            file_list_raw = list_data.get("list", []) or []

            # 处理文件列表
            file_list = []
            for item in file_list_raw:
                if isinstance(item, dict) and "fs_id" in item:
                    file_list.append(
                        {
                            "fs_id": item.get("fs_id", ""),
                            "path": item.get("path", ""),
                            "filename": item.get("server_filename", "未命名文件"),
                        }
                    )

            if not file_list:
                return {"status": "error", "message": "没有可转存的文件"}

            # 返回处理结果
            return {
                "status": "success",
                "file_list": file_list,
                "share_id": share_id,
                "user_id": user_id,
            }
        except Exception as e:
            logger.error(f"获取百度网盘分享文件列表异常: {str(e)}")
            return {"status": "error", "message": f"获取分享文件列表异常: {str(e)}"}

    async def _transfer_baidu_files(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        share_id: str,
        user_id: str,
        fs_id_list: List[str],
        save_path: str,
        bdstoken: str,
        is_single_file: bool = False,
    ) -> Dict[str, Any]:
        """
        转存百度网盘文件到指定目录

        参数:
            client: httpx 客户端
            headers: 请求头
            share_id: 分享ID
            user_id: 用户ID
            fs_id_list: 文件ID列表
            save_path: 保存路径
            bdstoken: bdstoken
            is_single_file: 是否为单文件

        返回:
            Dict[str, Any]: 转存结果，成功时包含to_fs_ids
        """
        try:
            # 准备转存URL和基本参数
            transfer_url = f"{self.baidu_base_url}share/transfer?shareid={share_id}&from={user_id}&bdstoken={bdstoken}&ondup=newcopy"

            # 单文件和多文件处理的区别
            if is_single_file and len(fs_id_list) == 1:
                # 单文件处理
                transfer_data = {"fsidlist": f"[{fs_id_list[0]}]", "path": save_path}
                timeout = 10.0  # 单文件设置更短的超时时间
            else:
                # 多文件处理
                transfer_data = {
                    "fsidlist": f"[{','.join(i for i in fs_id_list)}]",
                    "path": save_path,
                }
                timeout = DEFAULT_TIMEOUT

            # 执行转存请求
            transfer_response = await client.post(
                transfer_url, data=transfer_data, headers=headers, timeout=timeout
            )
            transfer_result = transfer_response.json()

            # 检查转存结果
            if transfer_result.get("errno") != 0:
                error_msg = transfer_result.get("show_msg", "未知错误")
                return {
                    "status": "error",
                    "message": f"文件转存失败: {error_msg}，错误码: {transfer_result.get('errno')}",
                }

            # 提取转存后的文件ID
            to_fs_ids = []
            if is_single_file and len(fs_id_list) == 1:
                # 单文件处理
                to_fs_id = (
                    transfer_result.get("extra", {})
                    .get("list", [{}])[0]
                    .get("to_fs_id")
                )
                if to_fs_id:
                    to_fs_ids = [to_fs_id]
            else:
                # 多文件处理
                for item in transfer_result.get("extra", {}).get("list", []):
                    if "to_fs_id" in item:
                        to_fs_ids.append(item["to_fs_id"])

            if not to_fs_ids:
                return {"status": "error", "message": "获取转存文件ID失败"}

            #  {'errno': 0, 'extra': {'list': [{'from': '/成龙历险记', 'from_fs_id': 1052365181042688, 'to': '/我的资源/成龙历险记(3)', 'to_fs_id': 72170287203887}]}, 'info': [{'errno': 0, 'fsid': 1052365181042688, 'path': '/成龙历险记'}], 'newno': '', 'request_id': 1732205881763648260, 'show_msg': '', 'task_id': 0}
            # 提取 to 路径
            to_path = (
                transfer_result.get("extra", {}).get("list", [{}])[0].get("to", "")
            )
            return {
                "status": "success",
                "to_fs_ids": to_fs_ids,
                "to": to_path,
            }
        except Exception as e:
            logger.error(f"转存百度网盘文件异常: {str(e)}")
            return {"status": "error", "message": f"转存文件异常: {str(e)}"}

    async def _create_baidu_share(
        self,
        fs_id_list: list,
        bdstoken: str,
        headers: Dict[str, str],
        is_single_file: bool = False,
        expiry_days: int = 1,
    ) -> Dict[str, Any]:
        """
        创建百度网盘分享链接

        参数:
            fs_id_list: 文件或目录的fsid列表
            bdstoken: 百度网盘token
            headers: 请求头
            expiry_days: 分享有效期(天)，支持1/7/30
            is_single_file: 是否为单文件

        返回:
            Dict[str, Any]: 分享结果信息，成功时包含share_url
        """
        logger.info(f"开始创建百度网盘分享链接，文件数量: {len(fs_id_list)}")
        try:
            # 生成随机提取码
            pwd = "".join(random.choices(string.ascii_lowercase + string.digits, k=4))

            # 有效期映射，0表示不设置有效期
            expiry_time = {1: "1", 7: "7", 30: "30", 0: "0"}.get(expiry_days, "1")

            # 分享API基本参数
            share_url = self.baidu_base_url + "share/set"
            params = {
                "channel": "chunlei",
                "bdstoken": bdstoken,
                "clienttype": "0",
                "app_id": "250528",
                "web": "1",
            }

            # 构建文件ID列表
            fs_ids_str = f"[{fs_id_list[0] if is_single_file else ','.join(str(fs_id) for fs_id in fs_id_list)}]"

            # 分享请求数据
            form_data = {
                "fid_list": fs_ids_str,
                "schannel": "4",
                "channel_list": "[]",
                "pwd": pwd,
                "period": expiry_time,
                "eflag_disable": "true",
            }

            # 发送分享请求
            async with httpx.AsyncClient(
                timeout=DEFAULT_TIMEOUT, verify=False
            ) as client:
                share_response = await client.post(
                    share_url, params=params, data=form_data, headers=headers
                )
                share_result = share_response.json()

                # 检查分享结果
                if share_result.get("errno") != 0:
                    logger.error(
                        f"创建分享失败: {share_result.get('show_msg', '未知错误')}"
                    )
                    return {
                        "status": "error",
                        "message": f"创建分享失败: {share_result.get('show_msg', '未知错误')}",
                    }

                # 构建完整的分享链接（带提取码）
                share_url_final = share_result.get("link", "") + f"?pwd={pwd}"
                logger.info(f"分享创建成功: {share_url_final}")

                return {
                    "status": "success",
                    "message": "分享创建成功",
                    "share_url": share_url_final,
                    "expiry_days": expiry_days,
                }
        except Exception as e:
            logger.error(f"创建百度网盘分享链接异常: {str(e)}")
            return {"status": "error", "message": f"发生异常: {str(e)}"}

    async def _create_baidu_flow_folder(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        bdstoken: str,
        save_path: str,
        dir_name: str = "更多资源访问【Pansoo.cn】",
    ) -> Dict[str, Any]:
        """
        创建百度网盘引流文件夹
        """
        try:
            url = f"{self.baidu_base_url}api/create"

            params = {
                "a": "commit",
                "bdstoken": bdstoken,
                "clienttype": "0",
                "app_id": "250528",
                "web": "1",
            }

            data = {
                "path": f"{save_path}/{dir_name}",
                "isdir": "1",
                "block_list": "[]",
            }
            # 请求参数是表单数据
            response = await client.post(url, params=params, data=data, headers=headers)
            result = response.json()
            logger.info(f"创建引流文件夹结果: {result}")
            if result.get("errno") != 0:
                return {
                    "status": "error",
                    "message": f"创建引流文件夹失败: {result.get('errmsg', '未知错误')}",
                }
            return {"status": "success", "message": "创建引流文件夹成功"}
        except Exception as e:
            logger.error(f"创建百度网盘引流文件夹异常: {str(e)}")
            return {"status": "error", "message": f"创建引流文件夹异常: {str(e)}"}

    async def _get_baidu_share_list_by_folder(
        self,
        client: httpx.AsyncClient,
        headers: Dict[str, str],
        uk: str,
        share_id: str,
        dir_path: str = "",
        bdstoken: Optional[str] = None,
        max_depth: int = 2,  # 限制递归深度
        current_depth: int = 0,  # 当前递归深度
    ) -> Dict[str, Any]:
        """
        获取百度网盘分享中指定文件夹的文件列表，支持递归获取子文件夹

        参数:
            client: httpx 客户端
            headers: 请求头
            uk: 分享创建者的用户ID
            share_id: 分享的ID
            dir_path: 要列出内容的目录路径
            bdstoken: bdstoken
            max_depth: 最大递归深度
            current_depth: 当前递归深度

        返回:
            Dict[str, Any]: 包含文件列表、过期时间等信息的字典
        """
        try:
            # 构建列表请求URL
            list_url = f"{self.baidu_base_url}share/list"
            params = {}

            if dir_path:
                # 常规文件夹列表获取
                logger.info(
                    f"获取分享文件夹列表: uk={uk}, share_id={share_id}, dir={dir_path}, depth={current_depth}/{max_depth}"
                )
                params = {
                    "uk": uk,
                    "shareid": share_id,
                    "dir": dir_path,
                    "bdstoken": bdstoken if bdstoken else "",
                    "channel": "chunlei",
                    "app_id": "250528",
                    "web": "1",
                    "order": "time",  # 按时间排序
                    "desc": "1",  # 降序
                    "showempty": "0",
                    "page": "1",
                    "num": "100",  # 获取较多文件
                    "clienttype": "0",
                }

            logger.info(f"请求share/list接口: {list_url}, params={params}")

            # 发送请求获取文件列表
            response = await client.get(list_url, params=params, headers=headers)
            data = response.json()

            # 检查响应状态
            if data.get("errno") != 0:
                logger.error(
                    f"获取文件列表失败: errno={data.get('errno')}, 路径={dir_path}"
                )
                return {
                    "status": "error",
                    "message": f"获取文件列表失败: {data.get('errno')}",
                }

            # 提取关键信息
            file_items = data.get("list", []) or []
            expired_type = data.get("expired_type", 0)  # 分享有效期类型
            title = data.get("title", "")  # 分享标题
            share_id = data.get("share_id", "")  # 分享ID
            uk = data.get("uk", "")  # 用户ID

            logger.info(
                f"获取到文件列表: 路径={dir_path}, 文件数量={len(file_items)}, 过期类型={expired_type}, 标题={title}"
            )

            # 处理文件列表，包含完整信息
            files = []
            dirs = []

            for item in file_items:
                file_info = {
                    "server_filename": item.get("server_filename", "未命名"),
                    "path": item.get("path", ""),
                    "server_mtime": item.get("server_mtime", 0),
                    "server_ctime": item.get("server_ctime", 0),
                    "category": item.get("category", 6),  # 默认为其他类型
                    "isdir": item.get("isdir", 0),
                    "size": item.get("size", 0),
                    "fs_id": item.get("fs_id", ""),
                }
                logger.info(f"文件信息: {file_info}")

                if file_info["isdir"] == 1:
                    dirs.append(file_info)
                    logger.info(
                        f"发现目录: {file_info['server_filename']}, 路径={file_info['path']}"
                    )
                else:
                    files.append(file_info)

            # 如果需要递归处理子目录且未超过最大深度
            all_files = files.copy()  # 先添加当前目录下的文件
            logger.info(
                f"当前目录 {dir_path} 下的文件数: {len(files)}, 子目录数: {len(dirs)}"
            )

            if current_depth < max_depth:
                for dir_item in dirs:
                    # 对每个子目录递归调用
                    sub_dir_path = dir_item["path"]
                    logger.info(
                        f"递归处理子目录: {sub_dir_path}, 当前深度={current_depth}, 最大深度={max_depth}"
                    )
                    sub_result = await self._get_baidu_share_list_by_folder(
                        client=client,
                        headers=headers,
                        uk=uk,
                        share_id=share_id,
                        dir_path=sub_dir_path,
                        bdstoken=bdstoken,
                        max_depth=max_depth,
                        current_depth=current_depth + 1,
                    )

                    if sub_result.get("status") == "success" and "files" in sub_result:
                        # 将子目录文件添加到总文件列表
                        sub_files = sub_result["files"]
                        logger.info(
                            f"子目录 {sub_dir_path} 返回文件数: {len(sub_files)}"
                        )
                        all_files.extend(sub_files)
                    else:
                        logger.warning(
                            f"获取子目录 {sub_dir_path} 失败: {sub_result.get('message', '未知错误')}"
                        )
            else:
                logger.info(f"已达到最大递归深度 {max_depth}，不再处理子目录")

            logger.info(f"目录 {dir_path} 处理完成，总文件数: {len(all_files)}")

            # 返回成功结果，包含更完整的信息
            return {
                "status": "success",
                "files": all_files,
                "dirs": dirs,
                "expired_type": expired_type,
                "title": title,
                "share_id": share_id,
                "uk": uk,
            }

        except httpx.HTTPStatusError as e:
            logger.error(
                f"获取分享文件夹列表HTTP状态错误: {e.response.status_code}, URL: {e.request.url}"
            )
            return {
                "status": "error",
                "message": f"HTTP状态错误: {e.response.status_code}",
            }
        except httpx.RequestError as e:
            logger.error(f"获取分享文件夹列表请求错误: {str(e)}, URL: {e.request.url}")
            return {"status": "error", "message": f"请求错误: {str(e)}"}
        except json.JSONDecodeError as e:
            logger.error(f"获取分享文件夹列表JSON解析错误: {str(e)}")
            return {"status": "error", "message": f"JSON解析错误: {str(e)}"}
        except Exception as e:
            logger.error(
                f"获取分享文件夹列表异常: {str(e)}, 堆栈: {traceback.format_exc()}"
            )
            return {"status": "error", "message": f"获取分享文件列表异常: {str(e)}"}

    async def _get_resource_author(
        self, uk: str, client: httpx.AsyncClient = None
    ) -> Dict[str, Any]:
        """
        从百度网盘获取资源作者信息

        参数:
            uk: 用户ID
            client: HTTP客户端，如果提供则使用该客户端（支持代理）

        返回:
            Dict[str, Any]: 包含作者名称和头像URL的字典
        """
        try:
            # 构建请求URL和参数
            url = f"{self.baidu_base_url}pcloud/user/getinfo"
            params = {
                "query_uk": uk,
                "third": "0",
                "channel": "chunlei",
                "web": "1",
                "app_id": "250528",
            }
            headers = {
                "Accept": "*/*",
                "Accept-Language": "zh-CN,zh;q=0.9",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Pragma": "no-cache",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            }

            # 如果提供了客户端，直接使用；否则创建新的客户端
            if client:
                # 使用提供的客户端（支持代理）
                response = await client.get(url, params=params, headers=headers)
                response_data = response.json()
            else:
                # 确保session已初始化
                if self.session is None:
                    await self.initialize()

                # 创建新的客户端，根据配置决定是否使用代理
                use_proxy = self.use_proxy_for_parsing
                async with get_client(use_proxy=use_proxy) as new_client:
                    response = await new_client.get(url, params=params, headers=headers)
                    response_data = response.json()

            # 检查响应状态
            if response_data.get("errno") != 0:
                logger.error(
                    f"获取资源作者失败: {response_data.get('errmsg', '未知错误')}"
                )
                return {
                    "status": "error",
                    "message": f"获取资源作者失败: {response_data.get('errmsg', '未知错误')}",
                }

            # 提取作者信息
            return {
                "status": "success",
                "author": response_data.get("user_info", {}).get("uname", ""),
                "avatar_url": response_data.get("user_info", {}).get("avatar_url", ""),
            }
        except Exception as e:
            logger.error(f"获取资源作者失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取资源作者失败: {str(e)}",
            }

    async def _get_resource_details(self, resource: PanResource) -> dict:
        """
        从百度网盘获取资源详情

        参数:
            resource: 需要获取详情的资源对象

        返回:
            dict: 资源详情字典，包含文件列表、分享ID、作者信息等
        """
        logger.info(
            f"开始获取资源详情: ID={resource.id}, 原始链接={resource.original_url}, 资源Key={resource.resource_key}"
        )

        # 从配置文件中获取cookie - 使用第二个账号（索引1）用于获取资源详情
        if (
            not self.accounts.get("baidu_accounts")
            or len(self.accounts["baidu_accounts"]) < 2
        ):
            logger.error("百度网盘账户列表为空或账户数量不足（需要至少2个账户）")
            return {"status": "error", "message": "百度网盘账户列表为空或账户数量不足"}

        # 使用第二个账号（索引1）获取资源详情，与转存功能使用不同账号避免冲突
        account = self.accounts["baidu_accounts"][1]
        cookie = account.get("cookie")
        if not cookie:
            logger.error("百度网盘第二个账号的cookie为空")
            return {"status": "error", "message": "百度网盘第二个账号的cookie为空"}

        logger.info(
            f"使用第二个账号（索引1）获取资源详情: {account.get('username', '未知用户')}"
        )

        # 构建请求头
        headers = {
            **self.baidu_headers,
            "Cookie": cookie,
            "Accept": "*/*",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Pragma": "no-cache",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "X-Requested-With": "XMLHttpRequest",
            "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
        }

        # 确保session已初始化
        if self.session is None:
            logger.info("初始化session")
            await self.initialize()

        # 使用共享HTTP客户端，根据配置决定是否使用代理
        use_proxy = self.use_proxy_for_parsing
        async with get_client(use_proxy=use_proxy) as client:
            # 1. 首先验证百度网盘链接是否有效
            logger.info(f"验证百度网盘链接有效性: {resource.original_url}")
            link_check_result = await self._check_baidu_link(resource.original_url)

            if not link_check_result.get("valid", False):
                logger.error(
                    f"百度网盘链接验证失败: {link_check_result.get('message')}"
                )
                return {"status": "error", "message": "链接失效"}

            logger.info(f"百度网盘链接验证通过: {link_check_result.get('message')}")

            # # 2. 统一获取bdstoken，无论有无密码，后续操作可能都需要
            # logger.info("获取bdstoken")
            # bdstoken_result = await self._get_baidu_bdstoken(client, headers)
            # if bdstoken_result.get("status") != "success":
            #     logger.error(f"获取bdstoken失败: {bdstoken_result.get('message')}")
            #     return {
            #         "status": "error",
            #         "message": "获取bdstoken失败，无法继续获取资源详情",
            #     }
            # bdstoken = bdstoken_result.get("bdstoken")

            # 调_extract_baidu_share_id 获取share_id
            share_info = await self._extract_baidu_share_id(
                resource.original_url, resource.share_pwd
            )

            logger.info(f"成功获取share_id: {share_info}")
            # 3. 如果有访问密码，验证并更新cookie
            if share_info["pwd"]:
                try:
                    # 验证分享链接并获取randsk
                    logger.info(f"验证分享密码: {share_info['pwd']}")
                    verify_result = await self._verify_baidu_share(
                        client,
                        headers,
                        share_info["surl"],
                        share_info["pwd"],
                        bdstoken=None,
                    )

                    if verify_result.get("status") != "success":
                        logger.error(
                            f"验证分享密码失败: {verify_result.get('message')}"
                        )
                        return {
                            "status": "error",
                            "message": "验证分享密码失败，无法获取资源详情",
                        }

                    # 更新cookie中的randsk
                    if verify_result.get("randsk"):
                        headers["Cookie"] = self.update_cookie(
                            verify_result.get("randsk"), headers["Cookie"]
                        )
                except Exception as e:
                    logger.error(
                        f"处理分享密码验证异常: {str(e)}, 堆栈: {traceback.format_exc()}"
                    )
                    return {
                        "status": "error",
                        "message": f"处理分享密码验证异常: {str(e)}",
                    }
            # 3. 直接调用share/list获取资源信息
            shorturl = resource.resource_key

            # 直接在URL中构建参数，不使用params
            list_url = f"{self.baidu_base_url}share/list?web=5&app_id=250528&desc=1&showempty=0&page=1&num=100&order=time&shorturl={shorturl[1:]}&root=1&view_mode=1&channel=chunlei&web=1"
            logger.info(f"share/list请求URL: {list_url}")
            # logger.info(f"share/list请求头信息: {headers}")

            try:
                response = await client.get(list_url, headers=headers)
                list_data = response.json()
                # 检查API调用是否成功
                if list_data.get("errno") != 0:
                    error_msg = list_data.get(
                        "show_msg",
                        f"获取资源信息失败，错误码: {list_data.get('errno')}",
                    )
                    logger.error(f"获取资源详情失败: {error_msg}, 响应: {list_data}")
                    return {"status": "error", "message": error_msg}

                logger.info(f"成功获取资源基本信息: errno={list_data.get('errno')}")
            except Exception as e:
                logger.error(
                    f"调用share/list接口异常: {str(e)}, 堆栈: {traceback.format_exc()}"
                )
                return {
                    "status": "error",
                    "message": f"调用share/list接口异常: {str(e)}",
                }

            # 提取关键信息
            share_id = list_data.get("share_id", "")
            uk = list_data.get("uk", "")
            expired_type = list_data.get("expired_type", 0)  # 分享有效期
            # 提取list内的文件名称
            title = list_data.get("list", [])[0].get("server_filename", "")  # 分享标题
            initial_file_list = list_data.get("list", []) or []

            logger.info(
                f"提取资源信息: share_id={share_id}, uk={uk}, expired_type={expired_type}, title={title}"
            )

            # 2. 获取作者信息
            logger.info(f"开始获取作者信息: uk={uk}")
            author_info = await self._get_resource_author(uk=uk, client=client)
            author_name = author_info.get("author", "") or ""
            author_avatar = author_info.get("avatar_url", "") or ""
            logger.info(f"获取到作者信息: author={author_name}")

            """
            业务逻辑:1、首先检测分享出来的是否仅有一个文件,如果是,则直接返回当前文件名;
            2、如果分享出来的是文件夹+文件或者文件夹+文件夹，则需要递归获取文件夹内的内容,并且需要将文件内容合并到最终的文件列表中；
            3、如果分享出来的是多个文件，则直接获取所有文件的内容合并到文件列表中；
            4、如果分享出来的是单文件夹内容，则需要递归获取文件夹内的内容,并且需要将文件内容合并到最终的文件列表中；
            """

            # 5. 获取完整的文件列表（包括递归获取子目录）
            logger.info(f"开始获取完整文件列表: share_id={share_id}, uk={uk}")

            all_files = []
            file_list_result = {"status": "success", "files": []}
            item_count = len(initial_file_list)

            if item_count == 0:
                logger.warning("分享链接中没有任何文件或文件夹。")
                # 保持 all_files 为空列表，后续流程会正常处理

            elif item_count == 1:
                item = initial_file_list[0]
                is_dir = item.get("isdir") == "1"

                if not is_dir:
                    # 情况一: 单文件分享
                    logger.info(f"检测到单文件分享: {item.get('server_filename')}")
                    all_files.append(item)
                    file_list_result["files"] = all_files
                else:
                    # 情况二: 单文件夹分享
                    fs_id = item.get("fs_id")
                    server_filename = item.get("server_filename")
                    logger.info(
                        f"检测到单文件夹分享，将深入获取内容: {server_filename}"
                    )
                    file_list_result = await self._get_baidu_share_list_by_folder(
                        client=client,
                        headers=headers,
                        uk=uk,
                        share_id=share_id,
                        dir_path=f"/sharelink{uk}-{fs_id}/{server_filename}",
                        # bdstoken=bdstoken,
                        max_depth=3,
                    )
                    all_files = file_list_result.get("files", [])

            else:  # item_count > 1
                # 情况三: 多文件、多文件夹或混合内容分享
                logger.info("检测到多文件/文件夹混合分享，开始分类处理。")

                files_at_root = []
                folders_to_scan = []
                for item in initial_file_list:
                    if item.get("isdir") == "1":
                        folders_to_scan.append(item)
                    else:
                        files_at_root.append(item)

                all_files.extend(files_at_root)  # 首先添加根目录下的所有文件
                logger.info(f"在根目录找到 {len(files_at_root)} 个文件。")

                for folder_item in folders_to_scan:
                    fs_id = folder_item.get("fs_id")
                    server_filename = folder_item.get("server_filename")
                    logger.info(f"正在递归扫描文件夹: {server_filename}")

                    sub_folder_result = await self._get_baidu_share_list_by_folder(
                        client=client,
                        headers=headers,
                        uk=uk,
                        share_id=share_id,
                        dir_path=f"/sharelink{uk}-{fs_id}/{server_filename}",
                        # bdstoken=bdstoken,
                        max_depth=3,
                    )

                    if sub_folder_result.get("status") == "success":
                        sub_files = sub_folder_result.get("files", [])
                        all_files.extend(sub_files)
                        logger.info(
                            f"从文件夹 '{server_filename}' 中获取了 {len(sub_files)} 个文件。"
                        )
                    else:
                        logger.warning(
                            f"扫描文件夹 '{server_filename}' 失败: {sub_folder_result.get('message')}"
                        )

                file_list_result["files"] = all_files

            if file_list_result.get("status") != "success":
                logger.error(f"获取文件列表失败: {file_list_result.get('message')}")
                return {
                    "status": "error",
                    "message": file_list_result.get("message", "获取文件列表失败"),
                }

            # 4. 处理文件列表：按修改时间排序，取最新的5个
            # 注意：all_files 现在由新的逻辑块填充
            logger.info(f"获取到所有文件: {len(all_files)}个")

            # 按server_mtime排序（降序）
            sorted_files = sorted(
                all_files, key=lambda x: int(x.get("server_mtime", 0)), reverse=True
            )

            # 提取前10个文件的文件名
            top_files = sorted_files[:10]
            top_filenames = [
                file.get("server_filename", "未命名") for file in top_files
            ]
            logger.info(f"提取前5个文件: {top_filenames}")

            # 提取前5个文件的类型
            top_file_types = [file.get("category", "6") for file in top_files]
            logger.info(f"提取前5个文件类型: {top_file_types}")

            # 5. 构建文件类型映射
            file_type_map = {
                1: "视频",
                2: "音频",
                3: "图片",
                4: "文档",
                5: "应用",
                6: "其他",
                7: "种子",
            }

            # 尝试确定主要文件类型,根据前5个文件的类型确定
            file_type = "其他"
            if top_files:
                categories = [file.get("category", "6") for file in top_files]
                # 找出出现最多的分类
                from collections import Counter

                category_counts = Counter(categories)
                most_common_category = category_counts.most_common(1)[0][0]
                file_type = file_type_map.get(most_common_category, "其他")
                logger.info(
                    f"确定主要文件类型: {file_type} (category={most_common_category})"
                )

            # 计算总文件大小
            total_size = sum(int(file.get("size", "0")) for file in all_files)
            human_size = "0B"
            if total_size < 1024:
                human_size = f"{total_size}B"
            elif total_size < 1024 * 1024:
                human_size = f"{total_size/1024:.2f}KB"
            elif total_size < 1024 * 1024 * 1024:
                human_size = f"{total_size/(1024*1024):.2f}MB"
            else:
                human_size = f"{total_size/(1024*1024*1024):.2f}GB"
            logger.info(f"计算总文件大小: {human_size} ({total_size}字节)")

            # 6. 返回成功结果
            logger.info(f"资源详情获取成功: {resource.resource_key}")
            return {
                "status": "success",
                "file_list": top_filenames,
                "all_files": all_files,  # 更改：返回所有文件信息而非top_files
                "share_id": share_id,
                "uk": uk,
                "expired_type": expired_type,  # 分享有效期
                "title": title or resource.title,  # 使用分享标题或现有标题
                "author_name": author_name,
                "author_avatar": author_avatar,
                "file_type": file_type,  # 文件类型
                "share_url": resource.original_url,  # 原始分享URL
                "file_size": human_size,  # 文件大小
            }

    async def fetch_and_update_resource_details(
        self, resource: PanResource, is_parsed: bool = True
    ) -> bool:
        """
        从百度网盘获取资源详情并更新资源对象

        参数:
            resource: 需要更新的资源对象

        返回:
            bool: 是否成功更新
        """
        logger.info(
            f"开始获取并更新资源详情: ID={resource.id}, 原始链接={resource.original_url}"
        )
        start_time = time.time()

        try:
            # 1. 调用 _get_resource_details 方法获取资源详情
            logger.info(f"调用_get_resource_details获取详情: {resource.resource_key}")
            resource_details = await self._get_resource_details(resource)
            if resource_details.get("status") == "error":
                logger.error(
                    f"获取资源详情失败: {resource_details.get('message', '未知错误')}"
                )
                return False

            # 2. 更新资源对象属性
            try:
                logger.info("开始更新资源对象属性")

                # 基本状态信息
                # 注意：不再强制设置is_parsed=True，保持用户提交时设置的值
                resource.verified_status = "valid"

                # 根据is_parsed参数决定是否填充字段
                if is_parsed:
                    # is_parsed=True: 填充完整字段
                    resource.share_url = resource.original_url  # 使用原始URL作为分享URL
                    logger.info(
                        f"is_parsed=True: 更新基本状态: verified_status={resource.verified_status}, is_parsed保持原值={resource.is_parsed}"
                    )

                    # 更新资源标题 - 检查是否为用户自定义标题
                    old_title = resource.title
                    # 如果当前标题不是默认的"待解析资源"格式，说明是用户自定义标题，保持不变
                    if old_title and not old_title.startswith("待解析资源:"):
                        logger.info(f"保持用户自定义标题不变: {old_title}")
                    else:
                        # 使用解析得到的标题
                        resource.title = (
                            resource_details.get("title", resource.title)
                            or resource.title
                        )
                        logger.info(f"更新标题: {old_title} -> {resource.title}")

                    # 更新资源作者
                    old_author = resource.author
                    resource.author = (
                        resource_details.get("author_name", resource.author)
                        or resource.author
                    )
                    resource.author_avatar = resource_details.get("author_avatar", "")
                    logger.info(f"更新作者: {old_author} -> {resource.author}")
                else:
                    # is_parsed=False: 仅填充基本字段，author设为97_bot
                    resource.author = "97_bot"
                    # 不填充share_url和author_avatar

                    # 更新资源标题 - 检查是否为用户自定义标题
                    old_title = resource.title
                    # 如果当前标题不是默认的"待解析资源"格式，说明是用户自定义标题，保持不变
                    if old_title and not old_title.startswith("待解析资源:"):
                        logger.info(
                            f"is_parsed=False: 保持用户自定义标题不变: {old_title}"
                        )
                    else:
                        # 使用解析得到的标题
                        resource.title = (
                            resource_details.get("title", resource.title)
                            or resource.title
                        )
                        logger.info(
                            f"is_parsed=False: 更新标题: {old_title} -> {resource.title}"
                        )
                    logger.info(
                        f"is_parsed=False: 设置author=97_bot，不填充share_url和author_avatar"
                    )

                # 处理文件列表
                file_list = resource_details.get("file_list", [])
                if isinstance(file_list, list) and file_list:
                    # 文件列表用换行符连接
                    resource.text_content = "\n".join(file_list)
                    logger.info(f"更新文件列表: {len(file_list)}个文件")
                else:
                    resource.text_content = "未获取到文件内容"
                    logger.warning("未获取到文件列表内容")

                # 更新文件类型
                resource.file_type = resource_details.get("file_type", "其他")
                resource.file_size = resource_details.get("file_size", "0B")
                logger.info(
                    f"更新文件类型和大小: {resource.file_type}, {resource.file_size}"
                )

                # 计算过期时间
                expired_type = resource_details.get("expired_type", 0)
                if expired_type > 0:
                    expiry_days = {1: 3, 7: 7, 30: 30}.get(expired_type, 0)
                    if expiry_days > 0:
                        resource.expiry_date = datetime.now(
                            BEIJING_TIMEZONE
                        ) + timedelta(days=expiry_days)
                        logger.info(
                            f"设置过期时间: {resource.expiry_date}, 类型={expired_type}, 天数={expiry_days}"
                        )

                # 确保日期字段有值
                if resource.created_at is None:
                    resource.created_at = datetime.now(BEIJING_TIMEZONE)
                    logger.info(f"设置创建时间: {resource.created_at}")

                # 更新时间: 使用server_mtime
                all_files_details = resource_details.get("all_files", [])
                if all_files_details:
                    latest_mtime = max(
                        int(f.get("server_mtime", 0)) for f in all_files_details
                    )
                    resource.updated_at = datetime.fromtimestamp(latest_mtime)
                    logger.info(
                        f"使用最新的文件修改时间更新updated_at: {resource.updated_at}"
                    )
                else:
                    resource.updated_at = datetime.now()
                    logger.warning("未找到文件列表，updated_at更新为当前时间")

                # 3. 保存更新到数据库
                await resource.save()
                total_time = time.time() - start_time
                logger.info(
                    f"资源详情更新成功并已保存. ID={resource.id}, 耗时={total_time:.2f}s"
                )
                return True

            except Exception as process_error:
                logger.error(f"处理资源详情数据失败: {str(process_error)}")
                logger.error(traceback.format_exc())
                return False
        except Exception as e:
            logger.error(f"更新资源详情失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    async def _make_request(
        self,
        client: httpx.AsyncClient,
        method: str,
        url: str,
        headers: Dict[str, str],
        **kwargs,
    ) -> Dict[str, Any]:
        """
        统一的HTTP请求处理方法

        参数:
            client: httpx客户端
            method: 请求方法 (GET, POST等)
            url: 请求URL
            headers: 请求头
            **kwargs: 其他请求参数

        返回:
            Dict[str, Any]: 响应结果，JSON格式
        """
        start_time = time.time()
        try:
            # 发送请求并计时
            response = await client.request(method, url, headers=headers, **kwargs)
            response.raise_for_status()
            result = response.json()

            # 记录耗时较长的请求
            elapsed_time = time.time() - start_time
            if elapsed_time > 1.0:
                logger.warning(f"请求耗时较长: {elapsed_time:.2f}秒, URL: {url}")

            return result
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP状态错误: {e.response.status_code}, URL: {url}")
            return {
                "status": "error",
                "message": f"HTTP状态错误: {e.response.status_code}",
            }
        except httpx.RequestError as e:
            logger.error(f"请求错误: {str(e)}, URL: {url}")
            return {"status": "error", "message": f"请求错误: {str(e)}"}
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {str(e)}, URL: {url}")
            return {"status": "error", "message": f"JSON解析错误: {str(e)}"}
        except Exception as e:
            logger.error(f"请求异常: {str(e)}, URL: {url}")
            return {"status": "error", "message": f"请求异常: {str(e)}"}


# 创建单例实例
def get_baidu_pan_service(use_proxy: bool = False) -> BaiduPanService:
    """
    获取百度网盘服务单例

    参数:
        use_proxy: 是否使用代理

    返回:
        BaiduPanService: 百度网盘服务实例
    """
    return BaiduPanService(use_proxy=use_proxy)


# 全局服务实例
baidu_pan_service = get_baidu_pan_service()
